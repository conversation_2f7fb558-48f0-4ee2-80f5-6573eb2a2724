# Spring Boot Configuration

# Server Configuration
server:
  port: ${APP_PORT:8081}

# Spring Configuration
spring:
  application:
    name: pda
  
  # Database Configuration
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:pdaDB}
    username: ${DB_USER:pdaRole}
    password: ${DB_PASSWORD:uy00cEdF5EazYyJa}
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: ${DB_POOL_MIN_SIZE:1}
      maximum-pool-size: ${DB_POOL_MAX_SIZE:10}
      max-lifetime: ${DB_MAX_LIFETIME:60000}
      connection-timeout: ${DB_ACQUISITION_TIMEOUT:5000}
      leak-detection-threshold: ${DB_LEAK_TIMEOUT:5000}
      validation-timeout: ${DB_VALIDATION_TIMEOUT:5000}
      idle-timeout: ${DB_IDLE_VALIDATION_TIMEOUT:30000}
      auto-commit: ${DB_AUTOCOMMIT:false}
  
  # JPA Configuration
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        multiTenancy: DATABASE
        tenant_identifier_resolver: br.com.celk.config.tenant.TenantIdentifierResolver
        multi_tenant_connection_provider: br.com.celk.config.tenant.MultiTenantConnectionProvider
  
  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_URL:https://keycloak.apoio.celk.info/auth}${KEYCLOAK_REALM_PATH:/realms/api}
          jwk-set-uri: ${KEYCLOAK_URL:https://keycloak.apoio.celk.info/auth}${KEYCLOAK_REALM_PATH:/realms/api}/protocol/openid-connect/certs

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Logging Configuration
logging:
  level:
    br.com.celk: ${LOG_LEVEL:INFO}
    org.springframework.security: DEBUG
  pattern:
    console: '%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n'

# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /docs
    enabled: true
  info:
    title: Celk API
    description: API para integração com parceiros.
    version: v1
    contact:
      name: CELK API Support
      email: <EMAIL>
      url: https://suporte.celk.com.br

# AWS Configuration
aws:
  dynamodb:
    endpoint: ${DYNAMO_DB_HOST:https://dynamodb.sa-east-1.amazonaws.com}:${DYNAMO_DB_PORT:443}
    region: ${DYNAMO_DB_REGION:sa-east-1}
    access-key: ${DYNAMO_DB_ACCESS_KEY:********************}
    secret-key: ${DYNAMO_DB_SECRET_KEY:A+4GOq2v1hyNLVeItpx4PW+Zxjhhcj9Smk4bKkoo}

# Custom Application Configuration
celk:
  datasource:
    config:
      metrics: ${DB_METRICS:true}
      min-size: ${DB_POOL_MIN_SIZE:1}
      max-size: ${DB_POOL_MAX_SIZE:10}
      max-lifetime: ${DB_MAX_LIFETIME:60}
      initial-size: ${DB_INITIAL_SIZE:1}
      acquisition-timeout: ${DB_ACQUISITION_TIMEOUT:5}
      leak-timeout: ${DB_LEAK_TIMEOUT:5}
      validation-timeout: ${DB_VALIDATION_TIMEOUT:5}
      idle-validation-timeout: ${DB_IDLE_VALIDATION_TIMEOUT:30}
      reap-timeout: ${DB_REAP_TIMEOUT:30}
      auto-commit: ${DB_AUTOCOMMIT:false}
      driver: ${DB_DRIVER:org.postgresql.Driver}
    limits:
      list-patients-limit: ${DB_LIMIT_PATIENTS:50}
      list-care-unit-limit: ${DB_LIMIT_CARE_UNITS:20}
      list-procedures-limit: ${DB_LIMIT_PROCEDURES:20}
      list-schedules-limit: ${DB_LIMIT_SCHEDULES:20}
      list-professionals-limit: ${DB_LIMIT_PROFESSIONALS:20}
      list-appointments-limit: ${DB_LIMIT_APPOINTMENTS:500}
      list-medicamentos-limit: ${DB_LIMIT_MEDICAMENTOS:500}

  # Usado para que o PDA possa se comunicar com o Saúde
  keycloak:
    user-realm: ${KEYCLOAK_USER_REALM:celk-realm}
    client-credentials: ${KEYCLOAK_CLIENT_CREDENTIALS:ck_saude}
    client-credentials-secret: ${KEYCLOAK_CLIENT_CREDENTIALS_SECRET:fce8f8eb-71a1-4f2c-a38b-e6270b14e118}

  dynamodb:
    table-name: ${DYNAMO_DB_TABLE_NAME:pda_database_data}

  saude:
    url: ${SAUDE_URL:http://tenant:8080.celk.com.br}

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: **********************************************************************************************************
    username: pdaRole
    password: uy00cEdF5EazYyJa

logging:
  level:
    br.com.celk: INFO

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    br.com.celk: INFO
    org.springframework.security: WARN
