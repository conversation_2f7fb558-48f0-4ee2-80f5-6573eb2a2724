package br.com.celk.service;

import br.com.celk.config.AuthClient;
import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.repository.ServiceScheduleGridRepository;
import br.com.celk.utils.PagingUtils;
import br.com.celk.web.builder.ClkListResponseBuilder;
import br.com.celk.web.dto.CancelSchedulingDTO;
import br.com.celk.web.dto.ClkGenericData;
import br.com.celk.web.dto.GridScheduleServiceDTO;
import br.com.celk.web.dto.SchedulingDTO;
import br.com.celk.web.dto.params.ScheduleCommonParamDTO;
import br.com.celk.web.dto.params.ScheduleParamDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import br.com.celk.web.mapper.ScheduleParamMapperImpl;
import io.quarkus.oidc.runtime.OidcJwtCallerPrincipal;
import io.quarkus.security.identity.SecurityIdentity;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.Response;
import java.security.Principal;
import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class ServiceScheduleGridService {

    private Client client;
    private String tenantName;

    @Inject
    ServiceScheduleGridRepository scheduleRepository;

    @Inject
    CelkDataSourceLimits dataSourceLimits;

    @Inject
    DynamoDynamoDbConfigService dynamoDbConfigService;

    @Inject
    AuthClient authClient;

    @Inject
    SecurityIdentity securityIdentity;

    @PostConstruct
    public void intiClient() {
        this.client = ClientBuilder.newBuilder().register(this.authClient.getCredentials()).build();
        this.tenantName = getTenantName();
    }

    private String getTenantName(){
        Principal principal = this.securityIdentity.getPrincipal();
        Optional<String> tenantNameOpt = ((OidcJwtCallerPrincipal) principal).claim("tenant_name");
        return tenantNameOpt.orElseThrow(() -> new IllegalStateException("Invalid tenant name."));
    }

    public ClkPaginatedListResponse<List<GridScheduleServiceDTO>> listSchedules(@Valid ScheduleParamDTO scheduleParamDTO) {

        return new ClkListResponseBuilder<List<GridScheduleServiceDTO>>().builder()
                   .setData(this.scheduleRepository.listSchedules(scheduleParamDTO))
                   .setPaging(PagingUtils.getPaging(scheduleParamDTO.getPageNumber(), dataSourceLimits.getListSchedulesLimit()))
                   .build();
    }

    public ClkGenericData<Long> countSchedules(@Valid ScheduleCommonParamDTO scheduleCommonParamDTO) {
        return new ClkGenericData<>(this.scheduleRepository.countSchedules(new ScheduleParamMapperImpl().toScheduleParamDTO(scheduleCommonParamDTO)));
    }

    public Response saveScheduling(@Valid List<SchedulingDTO> schedulingReserveDTOS) {
        return this.client.target(dynamoDbConfigService.getUrlDomain(this.tenantName) + "/app-cidadao/v1/scheduling")
               .request()
               .post(Entity.json(schedulingReserveDTOS));
    }

    public Response cancelScheduling(@Valid List<CancelSchedulingDTO> schedulingReserveDTOS) {
        return this.client.target(dynamoDbConfigService.getUrlDomain(this.tenantName) + "/app-cidadao/v1/scheduling/cancel")
               .request()
               .put(Entity.json(schedulingReserveDTOS));
    }
}
