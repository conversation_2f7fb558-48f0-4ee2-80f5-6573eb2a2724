package br.com.celk.service;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.repository.ProfessionalRepositoryImpl;
import br.com.celk.utils.PagingUtils;
import br.com.celk.web.builder.ClkListResponseBuilder;
import br.com.celk.web.dto.ClkGenericData;
import br.com.celk.web.dto.ProfessionalDTO;
import br.com.celk.web.dto.params.PageParamDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import br.com.celk.web.mapper.ProfessionalMapperImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jakarta.validation.Valid;
import java.util.List;

@Service
public class ProfessionalService {

    @Autowired
    private ProfessionalRepositoryImpl professionalRepository;

    @Autowired
    private CelkDataSourceLimits dataSourceLimits;

    public ClkPaginatedListResponse<List<ProfessionalDTO>> listProfessionals(@Valid PageParamDTO pageParamDTO) {

        return new ClkListResponseBuilder<List<ProfessionalDTO>>().builder()
                   .setData(new ProfessionalMapperImpl().toDto(this.professionalRepository.listProfessionals(pageParamDTO)))
                   .setPaging(PagingUtils.getPaging(pageParamDTO.getPageNumber(), dataSourceLimits.getListProfessionalsLimit()))
                   .build();
    }

    public ClkGenericData<Long> countProfessionals() {
        return new ClkGenericData<>(this.professionalRepository.count());
    }
}
