package br.com.celk.service;

import br.com.celk.config.CelkConfig;
import io.quarkus.cache.CacheResult;
import org.jboss.logging.Logger;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.List;
import java.util.stream.Collectors;

@ApplicationScoped
public class DynamoDynamoDbConfigService extends DynamoDbConfigAbstractService {

    private static final Logger LOG = Logger.getLogger(DynamoDynamoDbConfigService.class);

    @Inject
    DynamoDbClient dynamoDB;

    public List<CelkConfig> findAll() {
        return dynamoDB.scanPaginator(scanRequest()).items().stream()
                .map(CelkConfig::from)
                .collect(Collectors.toList());
    }

    public List<String> findByUf(String uf) {
        List<CelkConfig> all = findAll();

        return all.stream()
                .filter(c -> c.getUF().equals(uf))
                .map(CelkConfig::getTenantName)
                .collect(Collectors.toList());
    }

    public List<CelkConfig> add(CelkConfig celkConfig) {
        dynamoDB.putItem(putRequest(celkConfig));
        return findAll();
    }

    public CelkConfig get(String tenantName) {
        CelkConfig cc = null;
        try {
            cc = CelkConfig.from(dynamoDB.getItem(getRequest(tenantName)).item());
        } catch (Exception e) {
            LOG.fatal(e.getMessage(), e.getCause());
        }
        return cc;
    }

    public String getIdCidade(String tenantName) {
        String idCidade = this.get(tenantName).getIdCidade();
        if (idCidade == null || idCidade.isEmpty()) {
            throw new IllegalStateException(
                    "O Parâmetro idCidade não foi definido para o tenant: " + tenantName + ". Favor entrar em contato com o administrador do sistema."
            );
        }
        return idCidade;
    }

    @CacheResult(cacheName = "url-domain")
    public String getUrlDomain(String tenantName) {
        return this.get(tenantName).getUrlDomain();
    }
}