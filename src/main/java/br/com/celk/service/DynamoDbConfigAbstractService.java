package br.com.celk.service;

import br.com.celk.config.CelkConfig;
import org.eclipse.microprofile.config.ConfigProvider;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.GetItemRequest;
import software.amazon.awssdk.services.dynamodb.model.PutItemRequest;
import software.amazon.awssdk.services.dynamodb.model.ScanRequest;

import java.util.HashMap;
import java.util.Map;

public abstract class DynamoDbConfigAbstractService {

    public static final String CELK_CONFIG_URL_DOMAIN = "urlDomain";
    public static final String CELK_CONFIG_URL_DATABASE_READING = "urlDatabaseReading";
    public static final String CELK_CONFIG_TENANT_NAME = "tenantName";
    public static final String CELK_CONFIG_URL_DATABASSE = "urlDatabase";
    public static final String CELK_CONFIG_PORT = "port";
    public static final String CELK_CONFIG_USER = "user";
    public static final String CELK_CONFIG_PASS = "pass";
    public static final String CELK_CONFIG_DBNAME = "dbname";
    public static final String CELK_CONFIG_UF = "UF";
    public static final String CELK_CONFIG_ID_CIDADE = "idCidade";

    public String getTableName() {
        return ConfigProvider.getConfig().getValue("celk.dynamodb.table-name", String.class);
    }

    protected ScanRequest scanRequest() {
        return ScanRequest.builder().tableName(getTableName())
                .attributesToGet(CELK_CONFIG_TENANT_NAME, CELK_CONFIG_URL_DOMAIN, CELK_CONFIG_URL_DATABASSE, CELK_CONFIG_URL_DATABASE_READING, CELK_CONFIG_PORT, CELK_CONFIG_USER,
                        CELK_CONFIG_PASS, CELK_CONFIG_DBNAME, CELK_CONFIG_UF, CELK_CONFIG_ID_CIDADE).build();
    }

    protected PutItemRequest putRequest(CelkConfig celkConfig) {
        Map<String, AttributeValue> item = new HashMap<>();
        item.put(CELK_CONFIG_TENANT_NAME, AttributeValue.builder().s(celkConfig.getTenantName()).build());
        item.put(CELK_CONFIG_URL_DOMAIN, AttributeValue.builder().s(celkConfig.getUrlDomain()).build());
        item.put(CELK_CONFIG_URL_DATABASSE, AttributeValue.builder().s(celkConfig.getUrlDatabase()).build());
        item.put(CELK_CONFIG_URL_DATABASE_READING, AttributeValue.builder().s(celkConfig.getUrlDatabaseReading()).build());
        item.put(CELK_CONFIG_PORT, AttributeValue.builder().s(celkConfig.getPort().toString()).build());
        item.put(CELK_CONFIG_ID_CIDADE, AttributeValue.builder().s(celkConfig.getIdCidade()).build());
        item.put(CELK_CONFIG_USER, AttributeValue.builder().s(celkConfig.getUser()).build());
        item.put(CELK_CONFIG_PASS, AttributeValue.builder().s(celkConfig.getPass()).build());
        item.put(CELK_CONFIG_DBNAME, AttributeValue.builder().s(celkConfig.getDbname()).build());
        item.put(CELK_CONFIG_UF, AttributeValue.builder().s(celkConfig.getUF()).build());

        return PutItemRequest.builder()
                .tableName(getTableName())
                .item(item)
                .build();
    }

    protected GetItemRequest getRequest(String tanantName) {
        Map<String, AttributeValue> key = new HashMap<>();
        key.put(CELK_CONFIG_TENANT_NAME, AttributeValue.builder().s(tanantName).build());

        return GetItemRequest.builder()
                .tableName(getTableName())
                .key(key)
                .attributesToGet(CELK_CONFIG_TENANT_NAME, CELK_CONFIG_URL_DOMAIN, CELK_CONFIG_URL_DATABASSE, CELK_CONFIG_URL_DATABASE_READING, CELK_CONFIG_PORT, CELK_CONFIG_USER,
                        CELK_CONFIG_PASS, CELK_CONFIG_DBNAME, CELK_CONFIG_UF, CELK_CONFIG_ID_CIDADE)
                .build();
    }
}
