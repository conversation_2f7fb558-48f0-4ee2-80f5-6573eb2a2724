package br.com.celk.service;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.config.exception.RestException;
import br.com.celk.config.tenant.TenantContext;
import br.com.celk.repository.AtendimentosRealizadosRepository;
import br.com.celk.utils.PagingUtils;
import br.com.celk.web.builder.ClkListResponseBuilder;
import br.com.celk.web.dto.AtendimentosRealizadosDTO;
import br.com.celk.web.dto.params.AtendimentosRealizadosParamDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.client.Client;
import java.util.List;

@ApplicationScoped
public class AtendimentosRealizadosService {
    private static final Logger LOG = Logger.getLogger(AtendimentosRealizadosService.class.getName());

    protected Client client;

    @Inject
    AtendimentosRealizadosRepository atendimentosRealizadosRepository;

    @Inject
    CelkDataSourceLimits dataSourceLimits;

    public ClkPaginatedListResponse<List<AtendimentosRealizadosDTO>> getAtendimentosRealizadosLista(AtendimentosRealizadosParamDTO atendimentosRealizadosParamDTO) throws RestException {
        LOG.info("Executando query fila de espera."
                .concat(" ::tenant = ".concat(TenantContext.getCurrentTenant()))
                .concat(" ::startDate = ".concat(atendimentosRealizadosParamDTO.getStartDate()))
                .concat(" ::pageNumber = " + atendimentosRealizadosParamDTO.getPageNumber()));
        return new ClkListResponseBuilder<List<AtendimentosRealizadosDTO>>().builder()
                .setData(this.atendimentosRealizadosRepository.getFilaAtendimentosRealizados(atendimentosRealizadosParamDTO))
                .setPaging(PagingUtils.getPaging(atendimentosRealizadosParamDTO.getPageNumber(), dataSourceLimits.getListAppointmentsLimit()))
                .build();
    }
}