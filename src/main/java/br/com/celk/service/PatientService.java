package br.com.celk.service;

import br.com.celk.config.AuthClient;
import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.config.exception.RestException;
import br.com.celk.config.tenant.TenantContext;
import br.com.celk.http.SaudeEndpointProvider;
import br.com.celk.repository.PatientRepositoryImpl;
import br.com.celk.utils.PagingUtils;
import br.com.celk.utils.webservice.RestUtil;
import br.com.celk.web.builder.ClkListResponseBuilder;
import br.com.celk.web.dto.ClkGenericData;
import br.com.celk.web.dto.EvolucaoProntuarioDTO;
import br.com.celk.web.dto.PatientDTO;
import br.com.celk.web.dto.params.CpfCnsParamDTO;
import br.com.celk.web.dto.params.PatientCommonParamDTO;
import br.com.celk.web.dto.params.PatientParamV1DTO;
import br.com.celk.web.dto.params.PatientParamV2DTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import br.com.celk.web.mapper.PatientMapperImpl;
import br.com.celk.web.mapper.PatientParamMapperImpl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;
import jakarta.validation.Valid;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.ClientBuilder;
import jakarta.ws.rs.client.Entity;
import jakarta.ws.rs.core.Response;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

@Service
public class PatientService {

    private static final Logger LOG = LoggerFactory.getLogger(PatientService.class);
    protected Client client;

    @Autowired
    private PatientRepositoryImpl patientRepository;

    @Autowired
    private CelkDataSourceLimits celkDataSourceLimits;

    @Autowired
    private SaudeEndpointProvider saudeEndpointProvider;

    @Autowired
    private AuthClient authClient;

    public void startTenantResolver() {
        this.patientRepository.startTenantResolver();
    }

    @PostConstruct
    public void intiClient() {
        this.client = ClientBuilder.newBuilder().register(this.authClient.getCredentials()).build();
    }

    public List<PatientDTO> listPatients(PatientParamV1DTO patientParamDTO) throws RestException {
        LOG.debugv("Params - ", patientParamDTO.toString());

        return new PatientMapperImpl().toDto(this.patientRepository.listPatients(patientParamDTO));
    }

    public ClkPaginatedListResponse<List<PatientDTO>> listPatientsV2(@Valid PatientParamV2DTO patientParamV2DTO) throws RestException {
        LOG.debugv("Params - ", patientParamV2DTO.toString());

        PatientParamV1DTO patientParamDTO = new PatientParamMapperImpl().toPatientParamV1DTO(patientParamV2DTO);

        return new ClkListResponseBuilder<List<PatientDTO>>().builder()
                .setData(new PatientMapperImpl().toDto(this.patientRepository.listPatients(patientParamDTO)))
                .setPaging(PagingUtils.getPaging(patientParamV2DTO.getPageNumber(), celkDataSourceLimits.getListPatientsLimit()))
                .build();
    }

    public ClkGenericData<Long> countPatients(@Valid PatientCommonParamDTO patientCommonParamDTO) throws RestException {
        return new ClkGenericData<>(this.patientRepository.countCareUnits(new PatientParamMapperImpl().toPatientParamV1DTO(patientCommonParamDTO)));
    }

    public Response listMedicamentosPaciente(CpfCnsParamDTO paramDTO) {
        patientRepository.startTenantResolver();
        String url = saudeEndpointProvider.getEndpointPacienteMedicamentosContinuos(TenantContext.getCurrentTenant());

        return this.client.target(url)
                .queryParam("cpf", paramDTO.getCpf())
                .queryParam("cns", paramDTO.getCns())
                .request().get();
    }

    public Response listExamesPaciente(CpfCnsParamDTO paramDTO) {
        patientRepository.startTenantResolver();
        String url = saudeEndpointProvider.getEndpointPacienteExame(TenantContext.getCurrentTenant());

        return this.client.target(url)
                .queryParam("cpf", paramDTO.getCpf())
                .queryParam("cns", paramDTO.getCns())
                .request().get();
    }

    public Response listAgendamentosPaciente(CpfCnsParamDTO paramDTO, String dataInicial, String dataFinal, Long status) throws RestException {
        if (status != null) validaStatusAgendaGradeAtendimento(status);
        if (dataInicial != null) validateDateFormat(dataInicial);
        if (dataFinal != null) validateDateFormat(dataFinal);
        patientRepository.startTenantResolver();
        String url = saudeEndpointProvider.getEndpointPacienteAgendamentos(TenantContext.getCurrentTenant());

        return this.client.target(url)
                .queryParam("cpf", paramDTO.getCpf())
                .queryParam("cns", paramDTO.getCns())
                .queryParam("dataInicial", dataInicial)
                .queryParam("dataFinal", dataFinal)
                .queryParam("status", status)
                .request().get();
    }

    public Response listVacinasPaciente(CpfCnsParamDTO paramDTO) {
        patientRepository.startTenantResolver();
        String url = saudeEndpointProvider.getEndpointVacinasPaciente(TenantContext.getCurrentTenant());

        return this.client.target(url)
                .queryParam("cpf", paramDTO.getCpf())
                .queryParam("cns", paramDTO.getCns())
                .request().get();
    }

    public Response findPaciente(String cpf, String cns) {
        patientRepository.startTenantResolver();
        String url = saudeEndpointProvider.getEndpointConsultarPaciente(TenantContext.getCurrentTenant());

        url = (!"".equals(cpf) && cpf != null) ? url.replace("{cpf}", cpf) : url.replace("{cpf}", "");
        url = (!"".equals(cns) && cns != null) ? url.replace("{cns}", cns) : url.replace("{cns}", "");
        return this.client.target(url).request().headers(RestUtil.getHeaders()).get();
    }

    public Response listProntuariosByPaciente(String cpf, String cns, String revisao) {
        patientRepository.startTenantResolver();
        String url = saudeEndpointProvider.listProntuariosByPaciente(TenantContext.getCurrentTenant());

        url = (!"".equals(cpf) && cpf != null) ? url.replace("{cpf}", cpf) : url.replace("{cpf}", "");
        url = (!"".equals(cns) && cns != null) ? url.replace("{cns}", cns) : url.replace("{cns}", "");
        url = (!"".equals(revisao) && revisao != null) ? url.replace("{revisao}", revisao) : url.replace("{revisao}", "0");

        return this.client.target(url).request().headers(RestUtil.getHeaders()).get();
    }

    public Response salvarProntuario(EvolucaoProntuarioDTO prontuarioDTO) throws IOException {
        patientRepository.startTenantResolver();
        String url = saudeEndpointProvider.saveProntuario(TenantContext.getCurrentTenant());
        return this.client.target(url).request().headers(RestUtil.getHeaders()).post(Entity.json(prontuarioDTO));
    }

    public void validaStatusAgendaGradeAtendimento(Long status) throws RestException {
        List<Long> statusList = List.of(1L, 2L, 3L, 4L, 5L);
        if (!statusList.contains(status)) {
            throw new RestException(Response.Status.BAD_REQUEST, "Status informado inválido");
        }
    }

    public void validateDateFormat(String date) throws RestException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setLenient(false);
        try {
            sdf.parse(date);
        } catch (ParseException e) {
            throw new RestException(Response.Status.BAD_REQUEST, "Formato de data incorreto");
        }
    }
}
