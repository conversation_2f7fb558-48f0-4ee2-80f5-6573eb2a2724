package br.com.celk.service;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.config.exception.RestException;
import br.com.celk.config.tenant.TenantContext;
import br.com.celk.repository.FilaGuiasPagasPrestadorRepository;
import br.com.celk.utils.PagingUtils;
import br.com.celk.web.builder.ClkListResponseBuilder;
import br.com.celk.web.dto.FilaGuiasPagasPrestadorDTO;
import br.com.celk.web.dto.params.FilaGuiasPagasPrestadorParamDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.client.Client;
import java.util.List;

@ApplicationScoped
public class FilaGuiasPagasPrestadorService {
    private static final Logger LOG = Logger.getLogger(FilaGuiasPagasPrestadorService.class.getName());

    protected Client client;

    @Inject
    FilaGuiasPagasPrestadorRepository filaGuiasPagasPrestadorRepository;

    @Inject
    CelkDataSourceLimits dataSourceLimits;

    public ClkPaginatedListResponse<List<FilaGuiasPagasPrestadorDTO>> getGuiasPagasPrestadorLista(FilaGuiasPagasPrestadorParamDTO filaGuiasPagasPrestadorParamDTO) throws RestException {
        LOG.info("Executando query fila de espera."
                .concat(" ::tenant = ".concat(TenantContext.getCurrentTenant()))
                .concat(" ::startDate = ".concat(filaGuiasPagasPrestadorParamDTO.getStartDate()))
                .concat(" ::pageNumber = " + filaGuiasPagasPrestadorParamDTO.getPageNumber()));
        return new ClkListResponseBuilder<List<FilaGuiasPagasPrestadorDTO>>().builder()
                .setData(this.filaGuiasPagasPrestadorRepository.getFilaGuiasPagasPrestador(filaGuiasPagasPrestadorParamDTO))
                .setPaging(PagingUtils.getPaging(filaGuiasPagasPrestadorParamDTO.getPageNumber(), dataSourceLimits.getListAppointmentsLimit()))
                .build();
    }
}