package br.com.celk.service;

import br.com.celk.config.AuthClient;
import br.com.celk.config.exception.RestException;
import br.com.celk.config.tenant.TenantContext;
import br.com.celk.http.SaudeEndpointProvider;
import br.com.celk.web.dto.RegistroOcorrenciaDTO;
import br.com.celk.web.dto.ScheduleReserveV2DTO;
import br.com.celk.web.dto.TimeScheduleDTO;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.Response;

@ApplicationScoped
public class AgendaService {

    private static final Logger LOG = Logger.getLogger(AgendaService.class.getName());

    @Inject
    SaudeEndpointProvider saudeEndpointProvider;
    @Inject
    AuthClient authClient;
    @Inject
    PatientService patientService;
    @Inject
    DynamoDynamoDbConfigService dynamoDbConfigService;

    protected Client client;

    @PostConstruct
    public void intiClient() {
        this.client = ClientBuilder.newBuilder().register(this.authClient.getCredentials()).build();
    }

    public Response consultarAgendasProcedimento() {
        return consultarAgendasProcedimento(null, null);
    }

    public Response consultarAgendasProcedimento(String cpf, String cns) {
        patientService.startTenantResolver();
        String url = saudeEndpointProvider.getEndpointAgendaProcedimentoList(TenantContext.getCurrentTenant());
        LOG.info("Chamando CS - ".concat(url));
        return this.client.target(url)
                .queryParam("cdUsuarioCadsus", null)
                .queryParam("cpf", cpf)
                .queryParam("cns", cns)
                .request().get();
    }

    public Response consultarAgendaHorarios(String careUnitId, String procedureId) throws RestException {
        patientService.startTenantResolver();
        if (careUnitId == null) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro careUnitId não pode estar null!");
        }
        if (procedureId == null) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro procedureId não pode estar null!");
        }

        String url = saudeEndpointProvider.getEndpointAgendaHorarios(TenantContext.getCurrentTenant());
        LOG.info("Chamando CS - ".concat(url));
        return this.client.target(url)
                .queryParam("unidadeAtendimentoId", careUnitId)
                .queryParam("procedimentoId", procedureId)
                .request().get();
    }

    public Response agendarPaciente(String cpf, String cns, Long careUnitId, Long timeScheduleId, Long gridScheduleServiceId, Long procedureId) throws RestException {
        patientService.startTenantResolver();
        if (StringUtils.isEmpty(cpf) && StringUtils.isEmpty(cns)) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametros cpf ou cns devem estar preenchidos!");
        }
        if (careUnitId == null) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro careUnitId não pode estar null!");
        }
        if (timeScheduleId == null) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro timeScheduleId não pode estar null!");
        }
        if (gridScheduleServiceId == null) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro gridScheduleServiceId não pode estar null!");
        }
        if (procedureId == null) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro procedureId não pode estar null!");
        }

        String url = saudeEndpointProvider.getEndpointAgendaAgendarPaciente(TenantContext.getCurrentTenant());
        String idCidade = dynamoDbConfigService.getIdCidade(TenantContext.getCurrentTenant());
        url = url.replace("{idMunicipio}", String.valueOf(idCidade));

        LOG.info("URL - ".concat(url));
        return this.client.target(url).request()
                .post(Entity.json(
                        new ScheduleReserveV2DTO(cpf, cns, null, careUnitId, new TimeScheduleDTO(timeScheduleId), gridScheduleServiceId, procedureId)
                ));
    }

    public Response registrarOcorrencia(Long idSolicitacao, String dataHora, String descricao, String nomeUsuario, Long tipoOcorrencia) throws RestException {
        patientService.startTenantResolver();
        if (idSolicitacao == null) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro idSolicitacao não pode estar null!");
        }
        if (StringUtils.isBlank(dataHora)) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro dataHora não pode estar null!");
        }
        if (StringUtils.isBlank(descricao)) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro descricao deve estar preenchido!");
        }
        if (StringUtils.isBlank(nomeUsuario)) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro nomeUsuario deve estar preenchido!");
        }
        if (tipoOcorrencia == null) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro tipoOcorrencia não pode estar null!");
        }

        String url = saudeEndpointProvider.getEndpointRegistraOcorrencia(TenantContext.getCurrentTenant());

        LOG.info("URL - ".concat(url));
        return this.client.target(url).request()
                .post(Entity.json(
                        new RegistroOcorrenciaDTO(idSolicitacao, dataHora, descricao, nomeUsuario, tipoOcorrencia)
                ));
    }

    public Response cancelarAgendamentoPaciente(Long idAgenda, String motivoCancelamento, Long reabrirSolicitacao) throws RestException {
        patientService.startTenantResolver();
        if (idAgenda == null) {
            throw new RestException(Response.Status.BAD_REQUEST, "Parametro idAgenda não pode estar null!");
        }

        String url = saudeEndpointProvider.getEndpointAgendaCancelarAgendamento(TenantContext.getCurrentTenant());
        String idCidade = dynamoDbConfigService.getIdCidade(TenantContext.getCurrentTenant());

        url = url.replace("{idMunicipio}", String.valueOf(idCidade));
        url = url.replace("{idAgenda}", String.valueOf(idAgenda));
        if (reabrirSolicitacao == null) {
            reabrirSolicitacao = 1L;
        }
        url = url.replace("{reabrirSolicitacao}", String.valueOf(reabrirSolicitacao));
        if (motivoCancelamento == null)
            motivoCancelamento = "";
        url = url.replace("{motivoCancelamento}", motivoCancelamento);
        LOG.info("Chamando CS - ".concat(url));
        return this.client.target(url).request().delete();
    }

    public Response getComprovanteAgendamento(Long cdAgendamento, String tipoRetorno) throws RestException {
        if (tipoRetorno == null) {
            tipoRetorno = "";
        }
        patientService.startTenantResolver();
        String url = saudeEndpointProvider.getEndpointComprovanteAgendamento(TenantContext.getCurrentTenant());
        LOG.info("Chamando CS - ".concat(url));
        return this.client.target(url)
                .queryParam("cdAgendamento", cdAgendamento)
                .queryParam("tipoRetorno", tipoRetorno)
                .request().get();
    }
}