package br.com.celk.service;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.repository.ProcedureRepository;
import br.com.celk.utils.PagingUtils;
import br.com.celk.web.builder.ClkListResponseBuilder;
import br.com.celk.web.dto.ClkGenericData;
import br.com.celk.web.dto.ProcedureDTO;
import br.com.celk.web.dto.params.PageParamDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import br.com.celk.web.mapper.ProcedureMapperImpl;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.Valid;
import java.util.List;

@ApplicationScoped
public class ProcedureService {

    @Inject
    ProcedureRepository procedureRepository;

    @Inject
    CelkDataSourceLimits dataSourceLimits;

    public ClkPaginatedListResponse<List<ProcedureDTO>> listProcedures(@Valid PageParamDTO pageParamDTO) {

        return new ClkListResponseBuilder<List<ProcedureDTO>>().builder()
                   .setData(new ProcedureMapperImpl().toDto(this.procedureRepository.listProcedures(pageParamDTO)))
                   .setPaging(PagingUtils.getPaging(pageParamDTO.getPageNumber(), dataSourceLimits.getListProceduresLimit()))
                   .build();
    }

    public ClkGenericData<Long> countProcedures() {
        return new ClkGenericData<>(this.procedureRepository.count());
    }
}
