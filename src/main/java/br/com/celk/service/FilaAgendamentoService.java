package br.com.celk.service;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.config.exception.RestException;
import br.com.celk.config.tenant.TenantContext;
import br.com.celk.repository.FilaAgendamentosRepository;
import br.com.celk.utils.PagingUtils;
import br.com.celk.web.builder.ClkListResponseBuilder;
import br.com.celk.web.dto.FilaAgendamentoDTO;
import br.com.celk.web.dto.params.FilaAgendamentoParamDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.List;

@ApplicationScoped
public class FilaAgendamentoService {
    private static final Logger LOG = Logger.getLogger(FilaAgendamentoService.class.getName());

    @Inject
    FilaAgendamentosRepository filaAgendamentoRepository;

    @Inject
    CelkDataSourceLimits dataSourceLimits;

    public ClkPaginatedListResponse<List<FilaAgendamentoDTO>> getFilaAgendamentoLista(FilaAgendamentoParamDTO filaAgendamentoParamDTO) throws RestException {
        LOG.info("Executando query fila de espera."
                .concat(" ::tenant = ".concat(TenantContext.getCurrentTenant()))
                .concat(" ::startDate = ".concat(filaAgendamentoParamDTO.getStartDate()))
                .concat(" ::pageNumber = " + filaAgendamentoParamDTO.getPageNumber()));
        return new ClkListResponseBuilder<List<FilaAgendamentoDTO>>().builder()
                .setData(this.filaAgendamentoRepository.getFilaAgendamento(filaAgendamentoParamDTO))
                .setPaging(PagingUtils.getPaging(filaAgendamentoParamDTO.getPageNumber(), dataSourceLimits.getListAppointmentsLimit()))
                .build();
    }
}
