package br.com.celk.service;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.config.exception.RestException;
import br.com.celk.config.tenant.TenantContext;
import br.com.celk.repository.ConsultaAgendamentosRepository;
import br.com.celk.utils.PagingUtils;
import br.com.celk.web.builder.ClkListResponseBuilder;
import br.com.celk.web.dto.ConsultaAgendamentoDTO;
import br.com.celk.web.dto.params.ConsultaAgendamentoParamDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.List;

@ApplicationScoped
public class ConsultaAgendamentoService {
    private static final Logger LOG = Logger.getLogger(ConsultaAgendamentoService.class.getName());

    @Inject
    ConsultaAgendamentosRepository consultaAgendamentoRepository;

    @Inject
    CelkDataSourceLimits dataSourceLimits;

    public ClkPaginatedListResponse<List<ConsultaAgendamentoDTO>> getConsultaAgendamentoLista(ConsultaAgendamentoParamDTO consultaAgendamentoParamDTO) throws RestException {
        LOG.info("Executando query consulta lista de agendamentos."
                .concat(" ::tenant = ".concat(TenantContext.getCurrentTenant()))
                .concat(" ::pageNumber = " + consultaAgendamentoParamDTO.getNumeroPagina()));
        return new ClkListResponseBuilder<List<ConsultaAgendamentoDTO>>().builder()
                .setData(this.consultaAgendamentoRepository.getConsultaAgendamento(consultaAgendamentoParamDTO))
                .setPaging(PagingUtils.getPaging(consultaAgendamentoParamDTO.getNumeroPagina(), dataSourceLimits.getListAppointmentsLimit()))
                .build();
    }
}
