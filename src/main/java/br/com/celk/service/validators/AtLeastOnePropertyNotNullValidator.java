package br.com.celk.service.validators;

import org.jboss.logging.Logger;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import static org.apache.commons.beanutils.PropertyUtils.getProperty;


public class AtLeastOnePropertyNotNullValidator implements ConstraintValidator<AtLeastOnePropertyNotNull, Object> {

    private static final Logger LOG = Logger.getLogger(AtLeastOnePropertyNotNullValidator.class.getName());

    private String[] fieldNames;

    @Override
    public void initialize(AtLeastOnePropertyNotNull constraintAnnotation) {
        this.fieldNames = constraintAnnotation.fieldNames();
    }

    @Override
    public boolean isValid(Object object, ConstraintValidatorContext constraintContext) {
        if (object == null) {
            return false;
        }

        try {
            for (String fieldName : this.fieldNames){
                Object property = getProperty(object, fieldName);
                if (property != null) {
                    return true;
                }
            }
            return false;

        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return false;
        }
    }
}
