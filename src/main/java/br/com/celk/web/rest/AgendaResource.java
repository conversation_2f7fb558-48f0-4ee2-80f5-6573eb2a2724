package br.com.celk.web.rest;

import br.com.celk.config.exception.RestException;
import br.com.celk.service.AgendaService;
import br.com.celk.service.ConsultaAgendamentoService;
import br.com.celk.service.PatientService;
import br.com.celk.web.dto.params.ConsultaAgendamentoParamDTO;
import br.com.celk.web.dto.params.CpfCnsParamDTO;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Prontuário do paciente - Agendamento", description = "Consultas e atualização do prontuário de pacientes.")
@Path("csaude/agenda")
public class AgendaResource {

    private static final Logger LOG = Logger.getLogger(AgendaResource.class.getName());

    @Inject
    AgendaService agendaService;
    @Inject
    PatientService patientService;
    @Inject
    ConsultaAgendamentoService consultaAgendamentoService;


    @GET
    @Timed
    @Path("/consultarAgendasProcedimento")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Json com dados das Agendas"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(summary = "Retorna as Agendas.", description = "Retorna as Agendas.")
    public Response consultarAgendasProcedimento(
            @QueryParam(value = "cpf") String cpf,
            @QueryParam(value = "cns") String cns
    ) throws RestException {
        CpfCnsParamDTO paramDTO = new CpfCnsParamDTO(cpf, cns);
        try {
            if (StringUtils.isNotEmpty(cpf) || StringUtils.isNotEmpty(cns)) {
                return agendaService.consultarAgendasProcedimento(cpf, cns);
            } else {
                return agendaService.consultarAgendasProcedimento();
            }
        } catch (Exception e) {
            LOG.debugv("Params - ", paramDTO.toString());
            LOG.error((e.getMessage()));
            return Response.serverError().entity(e.getMessage()).build();
        }
    }

    @GET
    @Timed
    @Path("/consultarAgendamentos")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Json com dados dos agendamentos"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(summary = "Retorna os agendamentos.", description = "Retorna os agendamentos.")
    public Response consultarAgendamentos(
            @QueryParam(value = "idTipoProcedimento") Long idTipoProcedimento,
            @QueryParam(value = "apenasAgendamentoNaoConfirmado") Boolean apenasAgendamentoNaoConfirmado,
            @QueryParam(value = "numeroPagina") int numeroPagina) throws RestException {
        ConsultaAgendamentoParamDTO paramDTO = new ConsultaAgendamentoParamDTO(idTipoProcedimento, apenasAgendamentoNaoConfirmado, numeroPagina);
        try {
            return Response.ok(consultaAgendamentoService.getConsultaAgendamentoLista(paramDTO)).build();
        } catch (Exception e) {
            LOG.debugv("Params - ", paramDTO.toString());
            LOG.error((e.getMessage()));
            return Response.serverError().entity(e.getMessage()).build();
        }
    }

    @GET
    @Timed
    @Path("/consultarAgendaHorario")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Json com horário das Agendas"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(summary = "Retorna os Horários das Agendas.", description = "Retorna os Horários das Agendas.")
    public Response consultarAgendaHorarios(
            @QueryParam(value = "careUnitId") String careUnitId,
            @QueryParam(value = "procedureId") String procedureId
    ) throws RestException {
        return agendaService.consultarAgendaHorarios(careUnitId, procedureId);
    }

    @POST
    @Timed
    @Path("/agendarPaciente")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Código de ok caso consiga agendar o paciente"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(summary = "Realiza o agendamento do horário para o paciente.", description = "Realiza o agendamento do horário para o paciente.")
    public Response agendarPaciente(
            @QueryParam(value = "cpf") String cpf,
            @QueryParam(value = "cns") String cns,
            @QueryParam(value = "careUnitId") Long careUnitId,
            @QueryParam(value = "timeScheduleId") Long timeScheduleId,
            @QueryParam(value = "gridScheduleServiceId") Long gridScheduleServiceId,
            @QueryParam(value = "procedureId") Long procedureId
    ) throws RestException {
        LOG.info("agendarPaciente");
        return agendaService.agendarPaciente(cpf, cns, careUnitId, timeScheduleId, gridScheduleServiceId, procedureId);
    }

    @POST
    @Timed
    @Path("/registrarOcorrencia")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Código de ok caso consiga registrar a ocorrência"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(summary = "Realiza oregistro da ocorrência para o paciente.", description = "Realiza oregistro da ocorrência para o paciente.")
    public Response registrarOcorrencia(
            @QueryParam(value = "idSolicitacao") Long idSolicitacao,
            @QueryParam(value = "dataHora") String dataHora,
            @QueryParam(value = "descricao") String descricao,
            @QueryParam(value = "nomeUsuario") String nomeUsuario,
            @QueryParam(value = "tipoOcorrencia") Long tipoOcorrencia
    ) throws RestException {
        LOG.info("registrarOcorrencia");
        return agendaService.registrarOcorrencia(idSolicitacao, dataHora, descricao, nomeUsuario, tipoOcorrencia);
    }

    @DELETE
    @Timed
    @Path("/cancelarAgendamentoPaciente")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Código de ok caso consiga cancelar o horário marcado."),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(summary = "Cancelar o agendamento do paciente.", description = "Cancelar o agendamento do paciente.")
    public Response cancelarAgendamentoPaciente(
            @QueryParam("idAgenda") Long idAgenda,
            @QueryParam("motivoCancelamento") String motivoCancelamento,
            @QueryParam("reabrirSolicitacao") Long reabrirSolicitacao
    ) throws RestException {
        return agendaService.cancelarAgendamentoPaciente(idAgenda, motivoCancelamento, reabrirSolicitacao);
    }

    @GET
    @Timed
    @Path("/comprovanteAgendamento")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Código de ok caso consiga gerar o comprovante de agendamento."),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(summary = "Gerar o comprovante de agendamento do paciente.", description = "Gerar o comprovante de agendamento do paciente.")
    public Response getComprovanteAgendamento(@QueryParam("cdAgendamento") @NotNull(message = "Parametro código do agendamento (cdAgendamento) não pode estar null!")
                                                  Long cdAgendamento, @QueryParam("tipoRetorno") String tipoRetorno) throws RestException {
        return agendaService.getComprovanteAgendamento(cdAgendamento, tipoRetorno);
    }

}