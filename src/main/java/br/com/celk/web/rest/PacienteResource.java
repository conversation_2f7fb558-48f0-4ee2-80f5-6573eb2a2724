package br.com.celk.web.rest;

import br.com.celk.service.PatientService;
import br.com.celk.web.dto.EvolucaoProntuarioDTO;
import br.com.celk.web.dto.params.CpfCnsParamDTO;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Prontuário do paciente - Consultas", description = "Consultas e atualização do prontuário de pacientes.")
@Path("csaude/paciente")
public class PacienteResource {

    private static final Logger LOG = Logger.getLogger(PacienteResource.class.getName());

    @Inject
    PatientService patientService;

    @GET
    @Timed
    @Path("/medicamentos")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Json com dados dos Medicamentos do Paciente"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(
            summary = "Retorna os medicamentos do Paciente.",
            description = "Retorna os medicamentos do Paciente.")
    public Response listMedicamentos(
            @QueryParam(value = "cpf") String cpf,
            @QueryParam(value = "cns") String cns
    ) {
        CpfCnsParamDTO paramDTO = new CpfCnsParamDTO(cpf, cns);
        try {
            return patientService.listMedicamentosPaciente(paramDTO);
        } catch (Exception e) {
            LOG.debugv("Params - ", paramDTO.toString());
            LOG.error((e.getMessage()));
            return Response.serverError().entity(e.getMessage()).build();
        }
    }


    @GET
    @Timed
    @Path("/exames")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Json com dados dos Exames do Paciente"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(
            summary = "Retorna os exames do Paciente.",
            description = "Retorna os exames do Paciente.")
    public Response listExames(
            @QueryParam(value = "cpf") String cpf,
            @QueryParam(value = "cns") String cns
    ) {
        CpfCnsParamDTO paramDTO = new CpfCnsParamDTO(cpf, cns);
        try {
            return patientService.listExamesPaciente(paramDTO);
        } catch (Exception e) {
            LOG.debugv("Params - ", paramDTO.toString());
            LOG.error((e.getMessage()));
            return Response.serverError().entity(e.getMessage()).build();
        }
    }

    @GET
    @Timed
    @Path("/consultar")
    @RolesAllowed({"app-cidadao", "read_patient"})
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Json com dados do Paciente"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(
            summary = "Retorna os Agendamentos do Paciente.",
            description = "Retorna os Agendamentos do Paciente.")
    public Response consultarPaciente(
            @QueryParam(value = "cpf") String cpf,
            @QueryParam(value = "cns") String cns
    ) {
        CpfCnsParamDTO paramDTO = new CpfCnsParamDTO(cpf, cns);
        try {
            return patientService.findPaciente(cpf, cns);
        } catch (Exception e) {
            LOG.debugv("Params - ", paramDTO.toString());
            LOG.error((e.getMessage()));
            return Response.serverError().entity(e.getMessage()).build();
        }
    }

    @GET
    @Timed
    @Path("/consultar/prontuario")
    @RolesAllowed("read_patient")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Json com dados do Paciente"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(
            summary = "Retorna os Agendamentos do Paciente.",
            description = "Retorna os Agendamentos do Paciente.")
    public Response consultarProntuarios(
            @QueryParam(value = "revisao") String revisao,
            @QueryParam(value = "cpf") String cpf,
            @QueryParam(value = "cns") String cns
    ) {
        CpfCnsParamDTO paramDTO = new CpfCnsParamDTO(cpf, cns);
        try {
            return patientService.listProntuariosByPaciente(cpf, cns, revisao);
        } catch (Exception e) {
            LOG.debugv("Params - ", paramDTO.toString());
            LOG.error((e.getMessage()));
            return Response.serverError().entity(e.getMessage()).build();
        }
    }

    @POST
    @Timed
    @Path("/salvar/prontuario")
    @RolesAllowed("read_patient")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Prontuário salvo com sucesso"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(
            summary = "Retorna os Agendamentos do Paciente.",
            description = "Retorna os Agendamentos do Paciente.")
    public Response salvarProntuario(EvolucaoProntuarioDTO prontuarioDTO) {
        try {
            return patientService.salvarProntuario(prontuarioDTO);
        } catch (Exception e) {
            LOG.debugv("Params - ", prontuarioDTO.toString());
            LOG.error((e.getMessage()));
            return Response.serverError().entity(e.getMessage()).build();
        }
    }

    @GET
    @Timed
    @Path("/agendamentos")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Json com dados dos Agendamentos do Paciente"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(
            summary = "Retorna os Agendamentos do Paciente.",
            description = "Retorna os Agendamentos do Paciente.")
    public Response listAgendamentos(
            @QueryParam(value = "cpf") String cpf,
            @QueryParam(value = "cns") String cns,
            @QueryParam(value = "dataInicial") String dataInicial,
            @QueryParam(value = "dataFinal") String dataFinal,
            @QueryParam(value = "status") Long status
    ) {
        CpfCnsParamDTO paramDTO = new CpfCnsParamDTO(cpf, cns);
        try {
            return patientService.listAgendamentosPaciente(paramDTO, dataInicial, dataFinal, status);
        } catch (Exception e) {
            LOG.debugv("Params - ", paramDTO.toString());
            LOG.error((e.getMessage()));
            return Response.serverError().entity(e.getMessage()).build();
        }
    }

    @GET
    @Timed
    @Path("/vacinas")
    @RolesAllowed("app-cidadao")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Json com dados das Vacinas do Paciente"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(
            summary = "Retorna as Vacinas do Paciente.",
            description = "Retorna as Vacinas do Paciente.")
    public Response listVacinas(
            @QueryParam(value = "cpf") String cpf,
            @QueryParam(value = "cns") String cns
    ) {
        CpfCnsParamDTO paramDTO = new CpfCnsParamDTO(cpf, cns);
        try {
            return patientService.listVacinasPaciente(paramDTO);
        } catch (Exception e) {
            LOG.debugv("Params - ", paramDTO.toString());
            LOG.error((e.getMessage()));
            return Response.serverError().entity(e.getMessage()).build();
        }
    }


}
