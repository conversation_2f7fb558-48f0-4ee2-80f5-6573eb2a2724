package br.com.celk.web.rest;

import br.com.celk.service.ServiceScheduleGridService;
import br.com.celk.utils.ConstraintViolationUtils;
import br.com.celk.web.dto.CancelSchedulingDTO;
import br.com.celk.web.dto.ClkGenericData;
import br.com.celk.web.dto.SchedulingDTO;
import br.com.celk.web.dto.params.ScheduleCommonParamDTO;
import br.com.celk.web.dto.params.ScheduleParamDTO;
import br.com.celk.web.dto.response.ClkErrorResponse;
import br.com.celk.web.dto.response.CountResponse;
import br.com.celk.web.dto.response.GridScheduleResponse;
import br.com.celk.web.dto.response.SchedulingResponse;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import javax.annotation.security.RolesAllowed;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.ConstraintViolationException;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.util.List;

@ApplicationScoped
@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Agendamentos")
@Path("/csaude/v1/schedules")
public class ScheduleResource {

    @Inject
    ServiceScheduleGridService scheduleService;

    @GET
    @Path("/")
    @Timed
    @RolesAllowed("read_scheduling")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = GridScheduleResponse.class))),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna as agendas disponíveis",
            description = "Retorna as agendas disponíveis para as unidades de saúde pertencentes a um determinado município.")
    public Response listSchedules(@BeanParam ScheduleParamDTO scheduleParamDTO) {
        try {
            return Response.ok(scheduleService.listSchedules(scheduleParamDTO)).build();
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        }
    }

    @GET
    @Path("/count")
    @Timed
    @RolesAllowed("read_scheduling")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = CountResponse.class))),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna a quantidade total de agendas disponíveis",
            description = "Retorna a quantidade total de agendas disponíveis para as unidades de saúde pertencentes a um determinado município.")
    public Response countSchedules(@BeanParam ScheduleCommonParamDTO scheduleCommonParamDTO) {
        try {
            return Response.ok(scheduleService.countSchedules(scheduleCommonParamDTO)).build();
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        }
    }

    @POST
    @Path("/")
    @RolesAllowed("write_scheduling")
    @APIResponses(value = {
        @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = SchedulingResponse.class))),
        @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Cria novas agendas",
            description = "Cria novas agendas para pacientes.")
    public Response saveScheduling(ClkGenericData<List<SchedulingDTO>> schedulingReserveDTOS) {
        try {
            return scheduleService.saveScheduling(schedulingReserveDTOS.getData());
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        }

    }

    @PUT
    @Path("/cancel")
    @RolesAllowed("write_scheduling")
    @APIResponses(value = {
        @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = SchedulingResponse.class))),
        @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Realiza o cancelamento de agendamentos",
            description = "Realiza o cancelamento de agendamentos marcados para pacientes.")
    public Response cancelScheduling(ClkGenericData<List<CancelSchedulingDTO>> schedulingReserveDTOS) {
        try {
            return scheduleService.cancelScheduling(schedulingReserveDTOS.getData());
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        }

    }

}
