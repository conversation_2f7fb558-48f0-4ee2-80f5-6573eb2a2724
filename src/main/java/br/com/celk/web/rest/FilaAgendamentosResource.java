package br.com.celk.web.rest;

import br.com.celk.config.exception.RestException;
import br.com.celk.service.FilaAgendamentoService;
import br.com.celk.utils.ConstraintViolationUtils;
import br.com.celk.web.dto.params.FilaAgendamentoParamDTO;
import br.com.celk.web.dto.response.ClkErrorResponse;
import br.com.celk.web.dto.response.FilaAgendamentoResponse;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import javax.annotation.security.RolesAllowed;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.ConstraintViolationException;
import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@ApplicationScoped
@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Fila de Agendamento", description = "Filas de agendamentos.")
@Path("/csaude/v1/filaAgendamentos")
public class FilaAgendamentosResource {

    @Inject
    FilaAgendamentoService filaAgendamentoService;

    @GET
    @Path("/")
    @Timed
    @RolesAllowed("mpsc")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = FilaAgendamentoResponse.class))),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna uma lista de paciente aguardando agendamento",
            description = "Retorna as agendas disponíveis para as unidades de saúde pertencentes a um determinado município.")
    public Response getFilaAgendamentosLista(@BeanParam @Valid FilaAgendamentoParamDTO filaAgendamentoParamDTO) {
        try {
            return Response.ok(filaAgendamentoService.getFilaAgendamentoLista(filaAgendamentoParamDTO)).build();
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        } catch (RestException e) {
            throw new RuntimeException(e);
        }
    }
}
