package br.com.celk.web.rest;

import br.com.celk.service.DynamoDynamoDbConfigService;
import br.com.celk.utils.ConstraintViolationUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/v1/api/municipios")
@Tag(name = "Utilitários", description = "Consultas auxiliares.")
public class DynamoResource {

    @Autowired
    private DynamoDynamoDbConfigService dynamoDbConfigService;

    @GetMapping("/{uf}")
    @Operation(
            summary = "Retorna uma lista de municipios clientes da Celk por UF."
    )
    public ResponseEntity<?> getFilaAgendamentosLista(@PathVariable("uf") @Valid String uf) {
        try {
            return ResponseEntity.ok(dynamoDbConfigService.findByUf(uf.toUpperCase()));
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolationSpring(e);
        }
    }
}
