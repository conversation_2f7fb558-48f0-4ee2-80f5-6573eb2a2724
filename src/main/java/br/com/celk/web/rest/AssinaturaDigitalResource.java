package br.com.celk.web.rest;

import br.com.celk.config.AuthClient;
import br.com.celk.http.SaudeEndpointProvider;
import br.com.celk.utils.JsonUtils;
import br.com.celk.utils.webservice.RestUtil;
import br.com.celk.web.dto.AssinaturaDigitalPscDTO;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.core.Response;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Assinatura Digital - Callback", description = "Callback do serviço de assinatura digital.")
@Path("/v1/api")
public class AssinaturaDigitalResource {

    private static final Logger LOG = Logger.getLogger(AssinaturaDigitalResource.class.getName());

    @Inject
    SaudeEndpointProvider saudeEndpointProvider;

    @Inject
    AuthClient authClient;

    protected Client client;

    @PostConstruct
    public void intiClient() {
        this.client = ClientBuilder.newBuilder().register(this.authClient.getCredentials()).build();
    }

    @GET
    @Timed
    @Path("/psc-syn-callback")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", description = "Dados utilizados no callback"),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = Response.class)))
    })
    @Operation(summary = "Retorno do callback para o saúde do certificado PSC da Syngular.", description = "Retorno do callback para o saúde do certificado PSC da Syngular.")
    public Response callbackPscSyngular (
            @QueryParam(value = "state") String state,
            @QueryParam(value = "code") String code) {
        if (!"".equals(state)) {
            try {
                String stateDecoded = new String(Base64.getDecoder().decode(state), StandardCharsets.UTF_8);
                AssinaturaDigitalPscDTO dto = (AssinaturaDigitalPscDTO) JsonUtils.toObject(stateDecoded, AssinaturaDigitalPscDTO.class);
                assert dto != null;

                String url = saudeEndpointProvider.enviarRetornoPsc(dto.getTenant(), code, state);
                return this.client.target(url).request().headers(RestUtil.getHeaders()).post(null);
            } catch (Exception e) {
                LOG.error(e);
            }
        }
        return Response.status(Response.Status.BAD_REQUEST).entity("State inválido").build();
    }
}