package br.com.celk.web.rest;

import br.com.celk.config.ProfileConfig;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.time.ZonedDateTime;
import java.util.TimeZone;

@Produces(MediaType.APPLICATION_JSON)
@Path("/develop")
public class DevelopResource {

    @Inject
    ProfileConfig profileConfig;

    @GET
    @Timed
    @Operation(hidden=true)
    @Path("/version")
    @RolesAllowed("developer")
    public Response version() {
        try {
            return Response.ok(profileConfig.getVersion()).build();
        } catch (Exception e) {
            return Response.serverError().build();
        }
    }

    @GET
    @Timed
    @Operation(hidden=true)
    @Path("/profile-quarkus")
    @RolesAllowed("developer")
    public Response profile() {
        try {
            return Response.ok(profileConfig.getActiveProfileQuarkus()).build();
        } catch (Exception e) {
            return Response.serverError().build();
        }
    }

    @GET
    @Timed
    @Operation(hidden=true)
    @Path("/timezone")
    @RolesAllowed("developer")
    public Response getTimezone() {
        try {
            return Response.ok(
            "Timezone atual: " + TimeZone.getDefault().getID() + " | Hora atual: " + ZonedDateTime.now()
            ).build();
        } catch (Exception e) {
            return Response.serverError().build();
        }
    }

}
