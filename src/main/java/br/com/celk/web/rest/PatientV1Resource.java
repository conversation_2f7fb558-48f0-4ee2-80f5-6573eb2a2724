package br.com.celk.web.rest;

import br.com.celk.config.exception.RestException;
import br.com.celk.service.PatientService;
import br.com.celk.web.dto.PatientDTO;
import br.com.celk.web.dto.params.PatientParamV1DTO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/api/patients")
@Tag(name = "Deprecated")
public class PatientV1Resource {

    @Autowired
    private PatientService patientService;

    @GetMapping("/")
    @PreAuthorize("hasRole('read_patient')")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = PatientDTO[].class)))
    @Operation(
            summary = "Retorna informações de pacientes",
            description = "Retorna determinas informações do prontuário de pacientes.")
    public List<PatientDTO> listPacientes(@Valid PatientParamV1DTO patientParamDTO) throws RestException {
        return this.patientService.listPatients(patientParamDTO);
    }
}
