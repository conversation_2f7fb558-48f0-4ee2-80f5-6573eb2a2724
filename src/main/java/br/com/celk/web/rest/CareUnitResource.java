package br.com.celk.web.rest;

import br.com.celk.service.CareUnitService;
import br.com.celk.utils.ConstraintViolationUtils;
import br.com.celk.web.dto.params.PageParamDTO;
import br.com.celk.web.dto.response.CareUnitListResponse;
import br.com.celk.web.dto.response.ClkErrorResponse;
import br.com.celk.web.dto.response.CountResponse;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import javax.annotation.security.RolesAllowed;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.ConstraintViolationException;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@ApplicationScoped
@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Unidades de atendimento")
@Path("/csaude/v1/careUnits")
public class CareUnitResource {

    @Inject
    CareUnitService careUnitService;

    @GET
    @Path("/")
    @Timed
    @RolesAllowed("read_care_unit")
    @APIResponses(value = {
        @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = CareUnitListResponse.class))),
        @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna as unidades de saúde",
            description = "Retorna as unidades de saúde pertencentes a um determinado município.")
    public Response listCareUnits(@BeanParam PageParamDTO pageParamDTO) {
        try {
            return Response.ok(careUnitService.listCareUnits(pageParamDTO)).build();
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        }
    }

    @GET
    @Path("/count")
    @Timed
    @RolesAllowed("read_care_unit")
    @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = CountResponse.class)))
    @Operation(
            summary = "Retorna a quantidade total de unidades de saúde",
            description = "Retorna a quantidade total de unidades de saúde pertencentes a um determinado município.")
    public Response countCareUnits() {
        return Response.ok(careUnitService.countCareUnits()).build();
    }
}
