package br.com.celk.web.rest;

import br.com.celk.config.exception.RestException;
import br.com.celk.service.PatientService;
import br.com.celk.utils.ConstraintViolationUtils;
import br.com.celk.web.dto.params.PatientCommonParamDTO;
import br.com.celk.web.dto.params.PatientParamV2DTO;
import br.com.celk.web.dto.response.ClkErrorResponse;
import br.com.celk.web.dto.response.CountResponse;
import br.com.celk.web.dto.response.PatientListResponse;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import javax.annotation.security.RolesAllowed;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.ConstraintViolationException;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@ApplicationScoped
@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Pacientes")
@Path("/csaude/v1/patients")
public class PatientV2Resource {

    @Inject
    PatientService patientService;

    @GET
    @Path("/")
    @Timed
    @RolesAllowed("read_patient")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = PatientListResponse.class))),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna informações de pacientes",
            description = "Retorna determinas informações do prontuário de pacientes.")
    public Response listPatientsV2(@BeanParam PatientParamV2DTO patientParamDTO) throws RestException {
        try {
            return Response.ok(patientService.listPatientsV2(patientParamDTO)).build();
        } catch (ConstraintViolationException constraintViolationException) {
            return ConstraintViolationUtils.treatConstraintViolation(constraintViolationException);
        }
    }

    @GET
    @Path("/count")
    @Timed
    @RolesAllowed("read_patient")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = CountResponse.class))),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna a quantidade total de pacientes",
            description = "Retorna a quantidade total de pacientes.")
    public Response countPatients(@BeanParam PatientCommonParamDTO patientCommonParamDTO) throws RestException {
        try {
            return Response.ok(patientService.countPatients(patientCommonParamDTO)).build();
        } catch (ConstraintViolationException constraintViolationException) {
            return ConstraintViolationUtils.treatConstraintViolation(constraintViolationException);
        }
    }
}
