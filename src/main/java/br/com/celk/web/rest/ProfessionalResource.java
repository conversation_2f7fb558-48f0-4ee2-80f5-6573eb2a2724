package br.com.celk.web.rest;

import br.com.celk.service.ProfessionalService;
import br.com.celk.utils.ConstraintViolationUtils;
import br.com.celk.web.dto.params.PageParamDTO;
import br.com.celk.web.dto.response.ClkErrorResponse;
import br.com.celk.web.dto.response.CountResponse;
import br.com.celk.web.dto.response.ProfessionalListResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.ConstraintViolationException;

@RestController
@RequestMapping("/csaude/v1/professionals")
@Tag(name = "Profissionais")
public class ProfessionalResource {

    @Autowired
    private ProfessionalService professionalService;

    @GetMapping("/")
    @PreAuthorize("hasRole('read_professional')")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ProfessionalListResponse.class))),
        @ApiResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna os profissionais",
            description = "Retorna determinadas informações dos profissionais da saúde pertencentes a um determinado município.")
    public ResponseEntity<?> listProfessionals(PageParamDTO pageParamDTO) {
        try {
            return ResponseEntity.ok(professionalService.listProfessionals(pageParamDTO));
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolationSpring(e);
        }
    }

    @GetMapping("/count")
    @PreAuthorize("hasRole('read_professional')")
    @Operation(
            summary = "Retorna a quantidade total de profissionais",
            description = "Retorna a quantidade total de profissionais da saúde pertencentes a um determinado município.")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = CountResponse.class)))
    public ResponseEntity<?> countProfessionals() {
        return ResponseEntity.ok(professionalService.countProfessionals());
    }
}
