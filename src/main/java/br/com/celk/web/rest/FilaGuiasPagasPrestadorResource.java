package br.com.celk.web.rest;

import br.com.celk.config.exception.RestException;
import br.com.celk.service.FilaGuiasPagasPrestadorService;
import br.com.celk.utils.ConstraintViolationUtils;
import br.com.celk.web.dto.params.FilaGuiasPagasPrestadorParamDTO;
import br.com.celk.web.dto.response.ClkErrorResponse;
import br.com.celk.web.dto.response.FilaGuiasPagasPrestadorResponse;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import javax.annotation.security.RolesAllowed;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.ConstraintViolationException;
import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@ApplicationScoped
@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Guias Pagas por Prestador", description = "Lista com Guias Pagas por Prestador.")
@Path("/csaude/v1/guiasPagasPrestador")
public class FilaGuiasPagasPrestadorResource {

    @Inject
    FilaGuiasPagasPrestadorService filaGuiasPagasPrestadorService;

    @GET
    @Path("/")
    @Timed
    @RolesAllowed("mpsc")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = FilaGuiasPagasPrestadorResponse.class))),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna uma lista de Guias Pagas por Prestador para o Ministério Público.",
            description = "Retorna uma lista de Guias Pagas por Prestador para o Ministério Público.")
    public Response getGuiasPagasPrestadorLista(@BeanParam @Valid FilaGuiasPagasPrestadorParamDTO filaGuiasPagasPrestadorParamDTO) {
        try {
            return Response.ok(filaGuiasPagasPrestadorService.getGuiasPagasPrestadorLista(filaGuiasPagasPrestadorParamDTO)).build();
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        } catch (RestException e) {
            throw new RuntimeException(e);
        }
    }
}
