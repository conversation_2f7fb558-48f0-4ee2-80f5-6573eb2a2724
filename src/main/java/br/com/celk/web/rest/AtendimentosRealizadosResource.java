package br.com.celk.web.rest;

import br.com.celk.config.exception.RestException;
import br.com.celk.service.AtendimentosRealizadosService;
import br.com.celk.utils.ConstraintViolationUtils;
import br.com.celk.web.dto.params.AtendimentosRealizadosParamDTO;
import br.com.celk.web.dto.response.AtendimentoRealizadoResponse;
import br.com.celk.web.dto.response.ClkErrorResponse;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import javax.annotation.security.RolesAllowed;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.ConstraintViolationException;
import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@ApplicationScoped
@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Atendimentos Realizados", description = "Lista com Atendimentos Realizados.")
@Path("/csaude/v1/atendimentosRealizados")
public class AtendimentosRealizadosResource {

    @Inject
    AtendimentosRealizadosService atendimentosRealizadosService;

    @GET
    @Path("/")
    @Timed
    @RolesAllowed("mpsc")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = AtendimentoRealizadoResponse.class))),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna uma lista de atendimentos realizados via consórcios para o Ministério Público.",
            description = "Retorna atendimentos realizados via consórcios para o Ministério Público.")
    public Response getAtendimentosRealizadosLista(@BeanParam @Valid AtendimentosRealizadosParamDTO atendimentosRealizadosParamDTO) {
        try {
            return Response.ok(atendimentosRealizadosService.getAtendimentosRealizadosLista(atendimentosRealizadosParamDTO)).build();
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        } catch (RestException e) {
            throw new RuntimeException(e);
        }
    }
}
