package br.com.celk.web.rest;

import br.com.celk.service.ProcedureService;
import br.com.celk.utils.ConstraintViolationUtils;
import br.com.celk.web.dto.params.PageParamDTO;
import br.com.celk.web.dto.response.ClkErrorResponse;
import br.com.celk.web.dto.response.CountResponse;
import br.com.celk.web.dto.response.ProcedureListResponse;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import javax.annotation.security.RolesAllowed;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.ConstraintViolationException;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@ApplicationScoped
@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Procedimentos")
@Path("/csaude/v1/procedures")
public class ProcedureResource {

    @Inject
    ProcedureService procedureService;

    @GET
    @Path("/")
    @Timed
    @RolesAllowed("read_procedure")
    @APIResponses(value = {
            @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ProcedureListResponse.class))),
            @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna os procedimentos",
            description = "Retorna os procedimentos disponíveis para unidades de saúde pertencentes a um determinado município.")
    public Response listProcedures(@BeanParam PageParamDTO pageParamDTO) {
        try {
            return Response.ok(procedureService.listProcedures(pageParamDTO)).build();
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        }
    }

    @GET
    @Path("/count")
    @Timed
    @RolesAllowed("read_procedure")
    @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = CountResponse.class)))
    @Operation(
            summary = "Retorna a quantidade total de procedimentos",
            description = "Retorna a quantidade total de procedimentos disponíveis para unidades de saúde pertencentes a um determinado município.")
    public Response countProcedures() {
        return Response.ok(procedureService.countProcedures()).build();
    }
}
