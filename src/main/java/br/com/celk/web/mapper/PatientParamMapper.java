package br.com.celk.web.mapper;

import br.com.celk.web.dto.params.PatientCommonParamDTO;
import br.com.celk.web.dto.params.PatientParamV1DTO;
import br.com.celk.web.dto.params.PatientParamV2DTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "cdi")
public interface PatientParamMapper {

    @Mapping(target = "offset", expression = "java(String.valueOf((patientParamV2DTO.getQueryPageNumber()) * patientParamV2DTO.getListPatientsLimit()))")
    PatientParamV1DTO toPatientParamV1DTO(PatientParamV2DTO patientParamV2DTO);

    PatientParamV1DTO toPatientParamV1DTO(PatientCommonParamDTO patientCommonParamDTO);

}
