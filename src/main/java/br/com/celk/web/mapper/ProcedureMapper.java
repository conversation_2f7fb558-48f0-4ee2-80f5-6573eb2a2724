package br.com.celk.web.mapper;

import br.com.celk.model.Procedure;
import br.com.celk.web.dto.ProcedureDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "cdi")
public interface ProcedureMapper {

    Procedure toEntity(ProcedureDTO procedureDTO);

    List<Procedure> toEntity(List<ProcedureDTO> procedureDTOS);

    ProcedureDTO toDto(Procedure procedure);

    List<ProcedureDTO> toDto(List<Procedure> procedures);

}
