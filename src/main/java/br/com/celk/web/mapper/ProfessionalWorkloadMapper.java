package br.com.celk.web.mapper;

import br.com.celk.model.ProfessionalWorkload;
import br.com.celk.web.dto.ProfessionalWorkloadDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "cdi")
public interface ProfessionalWorkloadMapper {

    ProfessionalWorkload toEntity(ProfessionalWorkloadDTO professionalWorkloadDTO);

    List<ProfessionalWorkload> toEntity(List<ProfessionalWorkloadDTO> professionalWorkloadDTOS);

    ProfessionalWorkloadDTO toDto(ProfessionalWorkload professionalWorkload);

    List<ProfessionalWorkloadDTO> toDto(List<ProfessionalWorkload> professionalWorkloads);

}
