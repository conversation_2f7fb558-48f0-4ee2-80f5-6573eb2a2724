package br.com.celk.web.mapper;

import br.com.celk.model.ScheduleGrid;
import br.com.celk.web.dto.ScheduleGridDTO;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(uses = {ScheduleMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface ScheduleGridMapper {

    ScheduleGrid toEntity(ScheduleGridDTO scheduleGridDTO);

    List<ScheduleGrid> toEntity(List<ScheduleGridDTO> scheduleGridDTOS);

    ScheduleGridDTO toDto(ScheduleGrid scheduleGrid);

    List<ScheduleGridDTO> toDto(List<ScheduleGrid> scheduleGrids);

}
