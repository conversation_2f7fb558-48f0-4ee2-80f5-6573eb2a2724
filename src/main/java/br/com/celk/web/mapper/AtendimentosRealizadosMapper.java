package br.com.celk.web.mapper;

import br.com.celk.model.AtendimentosRealizados;
import br.com.celk.web.dto.AtendimentosRealizadosDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "cdi")
public interface AtendimentosRealizadosMapper {

    AtendimentosRealizados toEntity(AtendimentosRealizadosDTO atendimentosRealizadosDTO);

    List<AtendimentosRealizados> toEntity(List<AtendimentosRealizadosDTO> atendimentosRealizadosDTOs);

    AtendimentosRealizadosDTO toDto(AtendimentosRealizados atendimentosRealizado);

    List<AtendimentosRealizadosDTO> toDto(List<AtendimentosRealizados> atendimentosRealizados);

}
