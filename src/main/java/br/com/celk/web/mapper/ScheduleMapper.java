package br.com.celk.web.mapper;

import br.com.celk.model.Schedule;
import br.com.celk.web.dto.ScheduleDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "cdi")
public interface ScheduleMapper {

    Schedule toEntity(ScheduleDTO scheduleDTO);

    List<Schedule> toEntity(List<ScheduleDTO> scheduleDTOS);

    ScheduleDTO toDto(Schedule schedule);

    List<ScheduleDTO> toDto(List<Schedule> schedules);

}
