package br.com.celk.web.mapper;

import br.com.celk.model.FilaAgendamento;
import br.com.celk.web.dto.FilaAgendamentoDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "cdi")
public interface FilaAgendamentoMapper {

    FilaAgendamento toEntity(FilaAgendamentoDTO filaAgendamentoDTO);

    List<FilaAgendamento> toEntity(List<FilaAgendamentoDTO> filaAgendamentoDTOs);

    FilaAgendamentoDTO toDto(FilaAgendamento filaAgendamento);

    List<FilaAgendamentoDTO> toDto(List<FilaAgendamento> filaAgendamentos);

}
