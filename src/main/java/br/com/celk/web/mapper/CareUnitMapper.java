package br.com.celk.web.mapper;

import br.com.celk.model.CareUnit;
import br.com.celk.web.dto.CareUnitDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "cdi")
public interface CareUnitMapper {

    @Mapping(source = "cityName", target = "city.description")
    @Mapping(source = "stateName", target = "city.state.description")
    CareUnit toEntity(CareUnitDTO careUnitDTO);

    List<CareUnit> toEntity(List<CareUnitDTO> careUnitDTO);

    @Mapping(source = "city.description", target = "cityName")
    @Mapping( source = "city.state.description", target = "stateName")
    CareUnitDTO toDto(CareUnit careUnit);

    List<CareUnitDTO> toDto(List<CareUnit> careUnit);

}
