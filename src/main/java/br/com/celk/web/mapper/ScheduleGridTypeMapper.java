package br.com.celk.web.mapper;

import br.com.celk.model.ScheduleGridType;
import br.com.celk.web.dto.ScheduleGridTypeDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "cdi")
public interface ScheduleGridTypeMapper {

    ScheduleGridType toEntity(ScheduleGridTypeDTO scheduleGridTypeDTO);

    List<ScheduleGridType> toEntity(List<ScheduleGridTypeDTO> scheduleGridTypeDTOS);

    ScheduleGridTypeDTO toDto(ScheduleGridType scheduleGridType);

    List<ScheduleGridTypeDTO> toDto(List<ScheduleGridType> scheduleGridTypes);

}
