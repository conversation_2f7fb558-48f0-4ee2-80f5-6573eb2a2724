package br.com.celk.web.mapper;

import br.com.celk.model.ScheduleTimeGrid;
import br.com.celk.web.dto.ScheduleTimeGridDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "cdi")
public interface ScheduleTimeGridMapper {

    ScheduleTimeGrid toEntity(ScheduleTimeGridDTO scheduleTimeGridDTO);

    List<ScheduleTimeGrid> toEntity(List<ScheduleTimeGridDTO> scheduleTimeGridDTOS);

    ScheduleTimeGridDTO toDto(ScheduleTimeGrid scheduleTimeGrid);

    List<ScheduleTimeGridDTO> toDto(List<ScheduleTimeGrid> scheduleTimeGrids);

}
