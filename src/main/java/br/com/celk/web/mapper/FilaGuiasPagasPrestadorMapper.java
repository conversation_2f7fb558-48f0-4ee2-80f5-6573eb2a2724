package br.com.celk.web.mapper;

import br.com.celk.model.FilaGuiasPagasPrestador;
import br.com.celk.web.dto.FilaGuiasPagasPrestadorDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "cdi")
public interface FilaGuiasPagasPrestadorMapper {

    FilaGuiasPagasPrestador toEntity(FilaGuiasPagasPrestadorDTO filaGuiasPagasPrestadorDTO);

    List<FilaGuiasPagasPrestador> toEntity(List<FilaGuiasPagasPrestadorDTO> filaGuiasPagasPrestadorDTOS);

    FilaGuiasPagasPrestadorDTO toDto(FilaGuiasPagasPrestador filaGuiasPagasPrestador);

    List<FilaGuiasPagasPrestadorDTO> toDto(List<FilaGuiasPagasPrestador> filaGuiasPagasPrestadores);

}
