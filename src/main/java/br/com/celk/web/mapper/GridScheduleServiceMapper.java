package br.com.celk.web.mapper;

import br.com.celk.model.GridScheduleService;
import br.com.celk.web.dto.GridScheduleServiceDTO;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(uses = {ScheduleTimeGridMapper.class, ScheduleGridMapper.class, ScheduleGridTypeMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface GridScheduleServiceMapper {

    GridScheduleService toEntity(GridScheduleServiceDTO gridScheduleServiceDTO);

    List<GridScheduleService> toEntity(List<GridScheduleServiceDTO> gridScheduleServiceDTOS);

    GridScheduleServiceDTO toDto(GridScheduleService gridScheduleService);

    List<GridScheduleServiceDTO> toDto(List<GridScheduleService> gridScheduleServices);

}
