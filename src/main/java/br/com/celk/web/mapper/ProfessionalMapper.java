package br.com.celk.web.mapper;

import br.com.celk.model.Professional;
import br.com.celk.web.dto.ProfessionalDTO;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(uses = {ProfessionalWorkloadMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface ProfessionalMapper {

    Professional toEntity(ProfessionalDTO professionalDTO);

    List<Professional> toEntity(List<ProfessionalDTO> professionalDTOS);

    ProfessionalDTO toDto(Professional professional);

    List<ProfessionalDTO> toDto(List<Professional> professionals);

}
