package br.com.celk.web.mapper;

import br.com.celk.model.Patient;
import br.com.celk.web.dto.PatientDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "cdi")
public interface PatientMapper {

    Patient toEntity(PatientDTO patientDTO);

    List<Patient> toEntity(List<PatientDTO> patientDTOS);

    PatientDTO toDto(Patient patient);

    List<PatientDTO> toDto(List<Patient> patients);

}
