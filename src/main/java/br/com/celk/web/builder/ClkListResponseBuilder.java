package br.com.celk.web.builder;

import br.com.celk.web.dto.PagingDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;

public class ClkListResponseBuilder<T> {

    private ClkPaginatedListResponse<T> clkListResponse;

    public ClkListResponseBuilder<T> builder() {
        clkListResponse = new ClkPaginatedListResponse<>();
        return this;
    }

    public ClkListResponseBuilder<T> setData(T data) {
        this.clkListResponse.setData(data);
        return this;
    }

    public ClkListResponseBuilder<T> setPaging(PagingDTO paging) {
        this.clkListResponse.setPaging(paging);
        return this;
    }

    public ClkPaginatedListResponse<T> build() {
        return this.clkListResponse;
    }
}
