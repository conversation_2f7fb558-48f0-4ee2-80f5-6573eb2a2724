package br.com.celk.web.dto;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;
import java.time.OffsetDateTime;

@Schema(name="ScheduleTimeGrid")
public class ScheduleTimeGridDTO implements Serializable {

    private Long scheduleTimeGridId;
    private OffsetDateTime time;
    private Long status;
    private Long serviceScheduleGridId;

    public Long getScheduleTimeGridId() {
        return scheduleTimeGridId;
    }

    public void setScheduleTimeGridId(Long scheduleTimeGridId) {
        this.scheduleTimeGridId = scheduleTimeGridId;
    }

    public OffsetDateTime getTime() {
        return time;
    }

    public void setTime(OffsetDateTime time) {
        this.time = time;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Long getServiceScheduleGridId() {
        return serviceScheduleGridId;
    }

    public void setServiceScheduleGridId(Long serviceScheduleGridId) {
        this.serviceScheduleGridId = serviceScheduleGridId;
    }

    @Override
    public String toString() {
        return "ScheduleTimeGridDTO{" +
                "scheduleTimeGridId=" + scheduleTimeGridId +
                ", time=" + time +
                ", status=" + status +
                ", serviceScheduleGridId=" + serviceScheduleGridId +
                '}';
    }
}
