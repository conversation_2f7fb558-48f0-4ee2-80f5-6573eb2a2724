package br.com.celk.web.dto;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(name="GridScheduleService")
public class GridScheduleServiceDTO implements Serializable {

    private Long gridScheduleServiceId;
    private ScheduleGridDTO scheduleGrid;
    private ScheduleGridTypeDTO scheduleGridType;
    private Long averageProcedureTime;
    private List<ScheduleTimeGridDTO> timeSchedules;

    public Long getGridScheduleServiceId() {
        return gridScheduleServiceId;
    }

    public void setGridScheduleServiceId(Long gridScheduleServiceId) {
        this.gridScheduleServiceId = gridScheduleServiceId;
    }

    public ScheduleGridDTO getScheduleGrid() {
        return scheduleGrid;
    }

    public void setScheduleGrid(ScheduleGridDTO scheduleGrid) {
        this.scheduleGrid = scheduleGrid;
    }

    public ScheduleGridTypeDTO getScheduleGridType() {
        return scheduleGridType;
    }

    public void setScheduleGridType(ScheduleGridTypeDTO scheduleGridType) {
        this.scheduleGridType = scheduleGridType;
    }

    public Long getAverageProcedureTime() {
        return averageProcedureTime;
    }

    public void setAverageProcedureTime(Long averageProcedureTime) {
        this.averageProcedureTime = averageProcedureTime;
    }

    public List<ScheduleTimeGridDTO> getTimeSchedules() {
        return timeSchedules;
    }

    public void setTimeSchedules(List<ScheduleTimeGridDTO> timeSchedules) {
        this.timeSchedules = timeSchedules;
    }

    @Override
    public String toString() {
        return "GridScheduleServiceDTO{" +
                "gridScheduleServiceId=" + gridScheduleServiceId +
                ", scheduleGrid=" + scheduleGrid +
                ", scheduleGridType=" + scheduleGridType +
                ", averageProcedureTime=" + averageProcedureTime +
                ", timeSchedules=" + timeSchedules +
                '}';
    }
}


