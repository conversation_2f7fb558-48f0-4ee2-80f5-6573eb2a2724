package br.com.celk.web.dto;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(name="Professional")
public class ProfessionalDTO implements Serializable {

    private Long professionalId;
    private String professionalName;
    private List<ProfessionalWorkloadDTO> professionalWorkloads;

    public Long getProfessionalId() {
        return professionalId;
    }

    public void setProfessionalId(Long professionalId) {
        this.professionalId = professionalId;
    }

    public String getProfessionalName() {
        return professionalName;
    }

    public void setProfessionalName(String professionalName) {
        this.professionalName = professionalName;
    }

    public List<ProfessionalWorkloadDTO> getProfessionalWorkloads() {
        return professionalWorkloads;
    }

    public void setProfessionalWorkloads(List<ProfessionalWorkloadDTO> professionalWorkloads) {
        this.professionalWorkloads = professionalWorkloads;
    }

    @Override
    public String toString() {
        return "ProfessionalDTO{" +
                "professionalId=" + professionalId +
                ", professionalName='" + professionalName + '\'' +
                ", professionalWorkloads=" + professionalWorkloads +
                '}';
    }
}
