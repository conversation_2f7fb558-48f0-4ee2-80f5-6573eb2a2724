package br.com.celk.web.dto;

import br.com.celk.web.dto.response.ClkError;

import java.util.List;

public class ClkBatchGeneric {

    private Long correlationId;
    private Long code;
    private String message;
    private List<ClkError> errors;

    public Long getCorrelationId() {
        return correlationId;
    }

    public void setCorrelationId(Long correlationId) {
        this.correlationId = correlationId;
    }

    public Long getCode() {
        return code;
    }

    public void setCode(Long code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<ClkError> getErrors() {
        return errors;
    }

    public void setErrors(List<ClkError> errors) {
        this.errors = errors;
    }

    @Override
    public String toString() {
        return "ClkCreationResponse{" +
                "correlationId=" + correlationId +
                ", code=" + code +
                ", message='" + message + '\'' +
                ", errors=" + errors +
                '}';
    }
}
