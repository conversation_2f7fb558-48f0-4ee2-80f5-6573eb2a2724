package br.com.celk.web.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RegistroOcorrenciaDTO {

    private Long idSolicitacao;
    private String dataHora;
    private String descricao;
    private String nomeUsuario;
    private Long tipoOcorrencia;

    public RegistroOcorrenciaDTO(Long idSolicitacao, String dataHora, String descricao, String nomeUsuario, Long tipoOcorrencia) {
        this.idSolicitacao = idSolicitacao;
        this.dataHora = dataHora;
        this.descricao = descricao;
        this.nomeUsuario = nomeUsuario;
        this.tipoOcorrencia = tipoOcorrencia;
    }

    public Long getIdSolicitacao() {
        return idSolicitacao;
    }

    public void setIdSolicitacao(Long idSolicitacao) {
        this.idSolicitacao = idSolicitacao;
    }

    public String getDataHora() {
        return dataHora;
    }

    public void setDataHora(String dataHora) {
        this.dataHora = dataHora;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public Long getTipoOcorrencia() {
        return tipoOcorrencia;
    }

    public void setTipoOcorrencia(Long tipoOcorrencia) {
        this.tipoOcorrencia = tipoOcorrencia;
    }
}
