package br.com.celk.web.dto;

import java.io.Serializable;

public class ScheduleReserveDTO implements Serializable {

    private Long patientSaudeId;
    private Long careUnitId;
    private TimeScheduleDTO timeSchedule;
    private Long gridScheduleServiceId;
    private Long procedureId;

    public ScheduleReserveDTO(Long patientSaudeId, Long careUnitId, TimeScheduleDTO timeSchedule, Long gridScheduleServiceId, Long procedureId) {
        this.patientSaudeId = patientSaudeId;
        this.careUnitId = careUnitId;
        this.timeSchedule = timeSchedule;
        this.gridScheduleServiceId = gridScheduleServiceId;
        this.procedureId = procedureId;
    }

    public Long getPatientSaudeId() {
        return patientSaudeId;
    }

    public void setPatientSaudeId(Long patientSaudeId) {
        this.patientSaudeId = patientSaudeId;
    }

    public Long getCareUnitId() {
        return careUnitId;
    }

    public void setCareUnitId(Long careUnitId) {
        this.careUnitId = careUnitId;
    }

    public TimeScheduleDTO getTimeSchedule() {
        return timeSchedule;
    }

    public void setTimeSchedule(TimeScheduleDTO timeSchedule) {
        this.timeSchedule = timeSchedule;
    }

    public Long getGridScheduleServiceId() {
        return gridScheduleServiceId;
    }

    public void setGridScheduleServiceId(Long gridScheduleServiceId) {
        this.gridScheduleServiceId = gridScheduleServiceId;
    }

    public Long getProcedureId() {
        return procedureId;
    }

    public void setProcedureId(Long procedureId) {
        this.procedureId = procedureId;
    }
}
