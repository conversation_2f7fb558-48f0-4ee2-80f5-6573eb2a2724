package br.com.celk.web.dto.params;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.ws.rs.QueryParam;

public class ConsultaAgendamentoParamDTO {
    Long idTipoProcedimento;
    Boolean apenasAgendamentoNaoConfirmado;
    Boolean agendarConsultaViaPDA;

    @QueryParam("pageNumber")
    @Positive(message = "Page Number should be greater than 0")
    @NotNull(message = "Page Number is required")
    int numeroPagina;

    public ConsultaAgendamentoParamDTO() {
    }

    public ConsultaAgendamentoParamDTO(Long idTipoProcedimento, Boolean apenasAgendamentoNaoConfirmado, int numeroPagina) {
        this.idTipoProcedimento = idTipoProcedimento;
        this.apenasAgendamentoNaoConfirmado = apenasAgendamentoNaoConfirmado;
        this.numeroPagina = numeroPagina;
    }

    public Long getIdTipoProcedimento() {
        return idTipoProcedimento;
    }

    public void setIdTipoProcedimento(Long idTipoProcedimento) {
        this.idTipoProcedimento = idTipoProcedimento;
    }

    public Boolean getApenasAgendamentoNaoConfirmado() {
        return apenasAgendamentoNaoConfirmado;
    }

    public void setApenasAgendamentoNaoConfirmado(Boolean apenasAgendamentoNaoConfirmado) {
        this.apenasAgendamentoNaoConfirmado = apenasAgendamentoNaoConfirmado;
    }

    public int getNumeroPagina() {
        return numeroPagina;
    }

    public void setNumeroPagina(int numeroPagina) {
        this.numeroPagina = numeroPagina;
    }

    public Boolean getAgendarConsultaViaPDA() {
        return agendarConsultaViaPDA;
    }

    public void setAgendarConsultaViaPDA(Boolean agendarConsultaViaPDA) {
        this.agendarConsultaViaPDA = agendarConsultaViaPDA;
    }
}
