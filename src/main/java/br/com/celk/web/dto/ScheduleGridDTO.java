package br.com.celk.web.dto;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;

@Schema(name="ScheduleGrids")
public class ScheduleGridDTO implements Serializable {

    private Long scheduleGridId;
    private ScheduleDTO schedule;

    public Long getScheduleGridId() {
        return scheduleGridId;
    }

    public void setScheduleGridId(Long scheduleGridId) {
        this.scheduleGridId = scheduleGridId;
    }

    public ScheduleDTO getSchedule() {
        return schedule;
    }

    public void setSchedule(ScheduleDTO schedule) {
        this.schedule = schedule;
    }

    @Override
    public String toString() {
        return "ScheduleGridDTO{" +
                "scheduleGridId=" + scheduleGridId +
                ", schedule=" + schedule +
                '}';
    }
}
