package br.com.celk.web.dto;

import java.io.Serializable;

public class ScheduleReserveV2DTO implements Serializable {

    private String cpf;
    private String cns;
    private Long patientSaudeId;
    private Long careUnitId;
    private TimeScheduleDTO timeSchedule;
    private Long gridScheduleServiceId;
    private Long procedureId;

    public ScheduleReserveV2DTO(String cpf, String cns, Long patientSaudeId, Long careUnitId, TimeScheduleDTO timeSchedule, Long gridScheduleServiceId, Long procedureId) {
        this.cpf = cpf;
        this.cns = cns;
        this.patientSaudeId = patientSaudeId;
        this.careUnitId = careUnitId;
        this.timeSchedule = timeSchedule;
        this.gridScheduleServiceId = gridScheduleServiceId;
        this.procedureId = procedureId;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getCns() {
        return cns;
    }

    public void setCns(String cns) {
        this.cns = cns;
    }

    public Long getCareUnitId() {
        return careUnitId;
    }

    public void setCareUnitId(Long careUnitId) {
        this.careUnitId = careUnitId;
    }

    public TimeScheduleDTO getTimeSchedule() {
        return timeSchedule;
    }

    public void setTimeSchedule(TimeScheduleDTO timeSchedule) {
        this.timeSchedule = timeSchedule;
    }

    public Long getGridScheduleServiceId() {
        return gridScheduleServiceId;
    }

    public void setGridScheduleServiceId(Long gridScheduleServiceId) {
        this.gridScheduleServiceId = gridScheduleServiceId;
    }

    public Long getProcedureId() {
        return procedureId;
    }

    public void setProcedureId(Long procedureId) {
        this.procedureId = procedureId;
    }
}
