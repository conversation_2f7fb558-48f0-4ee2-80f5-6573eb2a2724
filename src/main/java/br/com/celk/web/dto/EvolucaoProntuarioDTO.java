package br.com.celk.web.dto;

import br.com.celk.utils.json.LongAdapter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.json.bind.annotation.JsonbTypeAdapter;


@JsonIgnoreProperties(ignoreUnknown = true)
public class EvolucaoProntuarioDTO {

    private String dataHistorico;
    private String descricao;
    private String dataRegistro;
    private String dataConclusao;

    private String cnesEmpresa;

    private String nomeProfissional;
    private String cnsProfissional;
    private String cboProfissional;
    private String numeroRegistroProfissional;
    private String ufRegistroProfissional;
    private String codigoConselhoClasse;

    @JsonbTypeAdapter(LongAdapter.class)
    private Long codigoPaciente;
    private String nomePaciente;
    private String sexoPaciente;
    private String cpfPaciente;
    private String dataNascimentoPaciente;
    @JsonbTypeAdapter(LongAdapter.class)
    private Long racaPaciente;

    private String codigoSigtap;
    private String cid;

    @JsonbTypeAdapter(LongAdapter.class)
    private Long tipoAtendimento;
    @JsonbTypeAdapter(LongAdapter.class)
    private Long nacionalidade;

    private NotificacaoCompulsoriaDTO notificacaoCompulsoriaDTO;

    public String getDataHistorico() {
        return dataHistorico;
    }

    public void setDataHistorico(String dataHistorico) {
        this.dataHistorico = dataHistorico;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataConclusao() {
        return dataConclusao;
    }

    public void setDataConclusao(String dataConclusao) {
        this.dataConclusao = dataConclusao;
    }

    public String getCnesEmpresa() {
        return cnesEmpresa;
    }

    public void setCnesEmpresa(String cnesEmpresa) {
        this.cnesEmpresa = cnesEmpresa;
    }

    public String getNomeProfissional() {
        return nomeProfissional;
    }

    public void setNomeProfissional(String nomeProfissional) {
        this.nomeProfissional = nomeProfissional;
    }

    public String getCnsProfissional() {
        return cnsProfissional;
    }

    public void setCnsProfissional(String cnsProfissional) {
        this.cnsProfissional = cnsProfissional;
    }

    public String getCboProfissional() {
        return cboProfissional;
    }

    public void setCboProfissional(String cboProfissional) {
        this.cboProfissional = cboProfissional;
    }

    public String getNumeroRegistroProfissional() {
        return numeroRegistroProfissional;
    }

    public void setNumeroRegistroProfissional(String numeroRegistroProfissional) {
        this.numeroRegistroProfissional = numeroRegistroProfissional;
    }

    public String getUfRegistroProfissional() {
        return ufRegistroProfissional;
    }

    public void setUfRegistroProfissional(String ufRegistroProfissional) {
        this.ufRegistroProfissional = ufRegistroProfissional;
    }

    public String getCodigoConselhoClasse() {
        return codigoConselhoClasse;
    }

    public void setCodigoConselhoClasse(String codigoConselhoClasse) {
        this.codigoConselhoClasse = codigoConselhoClasse;
    }

    public Long getCodigoPaciente() {
        return codigoPaciente;
    }

    public void setCodigoPaciente(Long codigoPaciente) {
        this.codigoPaciente = codigoPaciente;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getSexoPaciente() {
        return sexoPaciente;
    }

    public void setSexoPaciente(String sexoPaciente) {
        this.sexoPaciente = sexoPaciente;
    }

    public String getCpfPaciente() {
        return cpfPaciente;
    }

    public void setCpfPaciente(String cpfPaciente) {
        this.cpfPaciente = cpfPaciente;
    }

    public String getDataNascimentoPaciente() {
        return dataNascimentoPaciente;
    }

    public void setDataNascimentoPaciente(String dataNascimentoPaciente) {
        this.dataNascimentoPaciente = dataNascimentoPaciente;
    }

    public Long getRacaPaciente() {
        return racaPaciente;
    }

    public void setRacaPaciente(Long racaPaciente) {
        this.racaPaciente = racaPaciente;
    }

    public String getCodigoSigtap() {
        return codigoSigtap;
    }

    public void setCodigoSigtap(String codigoSigtap) {
        this.codigoSigtap = codigoSigtap;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public Long getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(Long tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public Long getNacionalidade() {
        return nacionalidade;
    }

    public void setNacionalidade(Long nacionalidade) {
        this.nacionalidade = nacionalidade;
    }

    public NotificacaoCompulsoriaDTO getNotificacaoCompulsoriaDTO() {
        return notificacaoCompulsoriaDTO;
    }

    public void setNotificacaoCompulsoriaDTO(NotificacaoCompulsoriaDTO notificacaoCompulsoriaDTO) {
        this.notificacaoCompulsoriaDTO = notificacaoCompulsoriaDTO;
    }

    @Override
    public String toString() {
        return "EvolucaoProntuarioDTO{" +
                "dataHistorico='" + dataHistorico + '\'' +
                ", descricao='" + descricao + '\'' +
                ", dataRegistro='" + dataRegistro + '\'' +
                ", dataConclusao='" + dataConclusao + '\'' +
                ", cnesEmpresa='" + cnesEmpresa + '\'' +
                ", nomeProfissional='" + nomeProfissional + '\'' +
                ", cnsProfissional='" + cnsProfissional + '\'' +
                ", cboProfissional='" + cboProfissional + '\'' +
                ", numeroRegistroProfissional='" + numeroRegistroProfissional + '\'' +
                ", ufRegistroProfissional='" + ufRegistroProfissional + '\'' +
                ", codigoConselhoClasse='" + codigoConselhoClasse + '\'' +
                ", codigoPaciente=" + codigoPaciente +
                ", nomePaciente='" + nomePaciente + '\'' +
                ", sexoPaciente='" + sexoPaciente + '\'' +
                ", cpfPaciente='" + cpfPaciente + '\'' +
                ", dataNascimentoPaciente='" + dataNascimentoPaciente + '\'' +
                ", racaPaciente=" + racaPaciente +
                ", codigoSigtap='" + codigoSigtap + '\'' +
                ", tipoAtendimento=" + tipoAtendimento +
                ", nacionalidade=" + nacionalidade +
                '}';
    }
}
