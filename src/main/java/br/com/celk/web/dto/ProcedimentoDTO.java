package br.com.celk.web.dto;

import java.io.Serializable;

public class ProcedimentoDTO implements Serializable {

    private String nomeProcedimento;
    private String dataAgendamento;
    private String dataConfirmacao;
    private String dataAtendimento;
    private String classificacaoRisco;

    private String unidadeDesejada;
    private String complexidade;
    private String categoriaProcedimento;

    public ProcedimentoDTO(String nomeProcedimento,
                           String dataAgendamento,
                           String dataConfirmacao,
                           String dataAtendimento,
                           String classificacaoRisco,
                           String unidadeDesejada,
                           String complexidade,
                           String categoriaProcedimento
    ) {
        this.nomeProcedimento = nomeProcedimento == null ? "" : nomeProcedimento;
        this.dataAgendamento = dataAgendamento == null ? "" : dataAgendamento;
        this.dataConfirmacao = dataConfirmacao == null ? "" : dataConfirmacao;
        this.dataAtendimento = dataAtendimento == null ? "" : dataAtendimento;
        this.classificacaoRisco = classificacaoRisco == null ? "" : classificacaoRisco;
        this.unidadeDesejada = unidadeDesejada == null ? "" : unidadeDesejada;
        this.complexidade = complexidade == null ? "" : complexidade;
        this.categoriaProcedimento = categoriaProcedimento == null ? "" : categoriaProcedimento;
    }


    public String getNomeProcedimento() {
        return nomeProcedimento;
    }

    public void setNomeProcedimento(String nomeProcedimento) {
        this.nomeProcedimento = nomeProcedimento;
    }

    public String getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(String dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public String getDataConfirmacao() {
        return dataConfirmacao;
    }

    public void setDataConfirmacao(String dataConfirmacao) {
        this.dataConfirmacao = dataConfirmacao;
    }

    public String getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(String dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public String getUnidadeDesejada() {
        return unidadeDesejada;
    }
    public void setUnidadeDesejada(String unidadeDesejada) {
        this.unidadeDesejada = unidadeDesejada;
    }
    public String getComplexidade() {
        return complexidade;
    }
    public void setComplexidade(String complexidade) {
        this.complexidade = complexidade;
    }

    public String getCategoriaProcedimento() {
        return categoriaProcedimento;
    }

    public void setCategoriaProcedimento(String categoriaProcedimento) {
        this.categoriaProcedimento = categoriaProcedimento;
    }


    public String getClassificacaoRisco() {
        return classificacaoRisco;
    }

    public void setClassificacaoRisco(String classificacaoRisco) {
        this.classificacaoRisco = classificacaoRisco;
    }
}
