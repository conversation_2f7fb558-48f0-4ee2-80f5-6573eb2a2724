package br.com.celk.web.dto.response;

import java.io.Serializable;

public class ClkError implements Serializable {

    private String message;
    private String field;

    public String getMessage() {
        return message;
    }

    public ClkError setMessage(String message) {
        this.message = message;
        return this;
    }

    public String getField() {
        return field;
    }

    public ClkError setField(String field) {
        this.field = field;
        return this;
    }

    @Override
    public String toString() {
        return "ClkError{" +
                "message='" + message + '\'' +
                ", field='" + field + '\'' +
                '}';
    }
}
