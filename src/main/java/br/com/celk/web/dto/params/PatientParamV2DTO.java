package br.com.celk.web.dto.params;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.ws.rs.QueryParam;

@Schema(name="PatientParamV2")
public class PatientParamV2DTO extends PatientCommonParamDTO {

    private int listPatientsLimit = 500;

    @QueryParam("pageNumber")
    @Positive(message = "Page number should greater than 0")
    @NotNull(message = "Page Number is required")
    private int pageNumber;

    public PatientParamV2DTO() {
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public int getQueryPageNumber() {
        return getPageNumber() - 1;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public int getListPatientsLimit() {
        return listPatientsLimit;
    }

    public void setListPatientsLimit(int listPatientsLimit) {
        this.listPatientsLimit = listPatientsLimit;
    }

    @Override
    public String toString() {
        return "PatientParamV2DTO{" +
                "listPatientsLimit=" + listPatientsLimit +
                ", pageNumber=" + pageNumber +
                "} " + super.toString();
    }
}
