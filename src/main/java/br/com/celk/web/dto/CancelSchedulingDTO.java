package br.com.celk.web.dto;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(name="CancelScheduling", description = "Model Object to cancel a Scheduling")
public class CancelSchedulingDTO implements Serializable {

    @NotNull(message = "Scheduling Id is required")
    private Long schedulingId;

    @NotNull(message = "Correlation Id is required")
    private Long correlationId;

    @NotBlank(message = "Cancellation Reason is required")
    private String cancellationReason;

    public String getCancellationReason() {
        return cancellationReason;
    }

    public void setCancellationReason(String cancellationReason) {
        this.cancellationReason = cancellationReason;
    }

    public Long getSchedulingId() {
        return schedulingId;
    }

    public void setSchedulingId(Long schedulingId) {
        this.schedulingId = schedulingId;
    }

    public Long getCorrelationId() {
        return correlationId;
    }

    public void setCorrelationId(Long correlationId) {
        this.correlationId = correlationId;
    }

    @Override
    public String toString() {
        return "CancelSchedulingDTO{" +
                "schedulingId=" + schedulingId +
                ", correlationId=" + correlationId +
                ", cancellationReason='" + cancellationReason + '\'' +
                '}';
    }
}
