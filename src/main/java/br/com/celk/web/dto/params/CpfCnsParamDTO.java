package br.com.celk.web.dto.params;

import br.com.celk.service.validators.AtLeastOnePropertyNotNull;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;

@Schema(name = "CpfCnsParamDTO")
@AtLeastOnePropertyNotNull(fieldNames = {"cpf", "cns"}, message = "At least one of the following parameters must be specified: CPF, CNS")
public class CpfCnsParamDTO implements Serializable {

    private String cpf;

    private String cns;

    public CpfCnsParamDTO(String cpf, String cns) {
        this.cpf = cpf;
        this.cns = cns;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getCns() {
        return cns;
    }

    public void setCns(String cns) {
        this.cns = cns;
    }

    @Override
    public String toString() {
        return "CpfCnsParamDTO{" +
                "cpf='" + cpf + '\'' +
                ", cns='" + cns + '\'' +
                '}';
    }
}
