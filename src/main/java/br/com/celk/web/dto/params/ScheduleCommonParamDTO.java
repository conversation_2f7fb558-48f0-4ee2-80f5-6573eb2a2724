package br.com.celk.web.dto.params;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import javax.ws.rs.QueryParam;
import java.io.Serializable;
import java.util.List;

@Schema(name="ScheduleCommonParam")
public class ScheduleCommonParamDTO implements Serializable {

    @QueryParam("careUnitId")
    @PositiveOrZero(message = "Care Unit Id should be equal or greater than 0")
    private Long careUnitId;

    @QueryParam("appointmentTypeId")
    private List<
            @Min(value = 8, message = "Appointment Type should between 8 and 9")
            @Max(value = 9, message = "Appointment Type should between 8 and 9")
                    Long> appointmentTypeIds;

    @QueryParam("professionalSpecialtyId")
    @PositiveOrZero(message = "Professional Specialty Id should be equal or greater than 0")
    private String professionalSpecialtyId;

    @QueryParam("professionalId")
    @PositiveOrZero(message = "Professional Id should be equal or greater than 0")
    private Long professionalId;

    @QueryParam("procedureId")
    @PositiveOrZero(message = "Procedure Id should be equal or greater than 0")
    private Long procedureId;

    @QueryParam("amountOfDays")
    @NotNull(message = "Amount of days is required")
    @Min( value = 1, message = "Amount of days should be between 1 and 30") @Max( value = 30, message = "Amount of days should be between 1 and 30")
    private Long amountOfDays;

    public Long getProcedureId() {
        return procedureId;
    }

    public void setProcedureId(Long procedureId) {
        this.procedureId = procedureId;
    }

    public Long getCareUnitId() {
        return careUnitId;
    }

    public void setCareUnitId(Long careUnitId) {
        this.careUnitId = careUnitId;
    }

    public String getProfessionalSpecialtyId() {
        return professionalSpecialtyId;
    }

    public void setProfessionalSpecialtyId(String professionalSpecialtyId) {
        this.professionalSpecialtyId = professionalSpecialtyId;
    }

    public Long getProfessionalId() {
        return professionalId;
    }

    public void setProfessionalId(Long professionalId) {
        this.professionalId = professionalId;
    }

    public Long getAmountOfDays() {
        return amountOfDays;
    }

    public void setAmountOfDays(Long amountOfDays) {
        this.amountOfDays = amountOfDays;
    }

    public List<Long> getAppointmentTypeIds() {
        return appointmentTypeIds;
    }

    public void setAppointmentTypeIds(List<Long> appointmentTypeIds) {
        this.appointmentTypeIds = appointmentTypeIds;
    }

    @Override
    public String toString() {
        return "ScheduleCommonParamDTO{" +
                "careUnitId=" + careUnitId +
                ", appointmentTypeIds=" + appointmentTypeIds +
                ", professionalSpecialtyId='" + professionalSpecialtyId + '\'' +
                ", professionalId=" + professionalId +
                ", procedureId=" + procedureId +
                ", amountOfDays=" + amountOfDays +
                '}';
    }
}
