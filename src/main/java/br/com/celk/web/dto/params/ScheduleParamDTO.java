package br.com.celk.web.dto.params;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.ws.rs.QueryParam;
import java.io.Serializable;

@Schema(name="ScheduleParam")
public class ScheduleParamDTO extends ScheduleCommonParamDTO implements Serializable {

    @QueryParam("pageNumber")
    @Positive(message = "Page Number should be greater than 0")
    @NotNull(message = "Page Number is required")
    private int pageNumber;

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    @Override
    public String toString() {
        return "ScheduleParamDTO{" +
                "pageNumber=" + pageNumber +
                "} " + super.toString();
    }
}
