package br.com.celk.web.dto.params;

import br.com.celk.service.validators.AtLeastOnePropertyNotNull;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import javax.ws.rs.QueryParam;
import java.io.Serializable;
import java.util.List;

@AtLeastOnePropertyNotNull(fieldNames={"cpf", "cns", "name", "motherName", "birthdate"},
        message = "At least one of the following parameters must be specified: CPF, CNS, Name, MotherName or Birthdate")
@Schema(name="PatientCommonParam")
public class PatientCommonParamDTO implements Serializable {

    @QueryParam("cpf")
    @Size(min = 11, max = 14, message = "CPF format is invalid")
    private String cpf;

    @Size(min = 15, max = 18, message = "CNS format is invalid")
    @QueryParam("cns")
    private String cns;

    @QueryParam("name")
    @Size(min = 5, message = "Name must contain at least 5 characters")
    private String name;

    @QueryParam("motherName")
    private String motherName;

    @QueryParam("birthdate")
    @Size(min = 10, max = 10, message = "Birthdate invalid (Valid format: dd-MM-yyyy)")
    private String birthdate;

    @QueryParam("cityName")
    private String cityName;

    @QueryParam("status")
    private List<@Pattern(regexp = "[0-3]", message = "Status must be one of the following options: 0(ACTIVE), 1(TEMPORARY), 2(INACTIVE) ou 3(DELETED)") String> status;

    @QueryParam("patientStatusTeam")
    @Pattern(regexp = "[0-1]", message = "PatientStatusTeam must be one of the fallowing options: 0(ONLY_PATIENTS_WITH_ASSOCIATED_TEAM) ou 1(ONLY_PATIENTS_WITHOUT_ASSOCIATED_TEAM)")
    private  String patientStatusTeam;

    public String getPatientStatusTeam() {
        return this.patientStatusTeam;
    }

    public void setPatientStatusTeam(String patientStatusTeam) {
        this.patientStatusTeam = patientStatusTeam;
    }

    public List<String> getStatus() {
        return this.status;
    }

    public void setStatus(List<String> status) {
        this.status = status;
    }

    public String getCityName() {
        return this.cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCpf() {
        return this.cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getCns() {
        return this.cns;
    }

    public void setCns(String cns) {
        this.cns = cns;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMotherName() {
        return this.motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public String getBirthdate() {
        return this.birthdate;
    }

    public void setBirthdate(String birthdate) {
        this.birthdate = birthdate;
    }

    @Override
    public String toString() {
        return "PatientCommonParamDTO{" +
                "cpf='" + cpf + '\'' +
                ", cns='" + cns + '\'' +
                ", name='" + name + '\'' +
                ", motherName='" + motherName + '\'' +
                ", birthdate='" + birthdate + '\'' +
                ", cityName='" + cityName + '\'' +
                ", status=" + status +
                ", patientStatusTeam='" + patientStatusTeam + '\'' +
                '}';
    }
}
