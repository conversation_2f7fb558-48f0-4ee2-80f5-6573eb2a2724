package br.com.celk.web.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;

@Schema(name = "AtendimentosRealizados")
@JsonInclude()
public class AtendimentosRealizadosDTO implements Serializable {

    private String idGuia;
    private String situacao;
    private String chaveRegulacao;
    private String sistemaRegulacao;
    private String codCadSus;
    private String nomePaciente;
    private String cpfPaciente;
    private String cnsPaciente;
    private String codigoIbgeOrigemPaciente;
    private String municipioOrigemPaciente;
    private String unidadeExecutante;
    private String codigoIbgeUnidadeExecutante;
    private String municipioUnidadeExecutante;
    private String codigoProcedimentoSIGTAP;
    private String descricaoProcedimentoSIGTAP;
    private String codigoProcedimentoOriginal;
    private String descricaoProcedimentoOriginal;
    private String dataAgendamento;
    private String dataAtendimento;
    private String dataFaturamento;
    private String procedimentoComplementar;
    private String justificativa;

    public String getIdGuia() {
        return idGuia;
    }

    public void setIdGuia(String idGuia) {
        this.idGuia = idGuia;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getChaveRegulacao() {
        return chaveRegulacao;
    }

    public void setChaveRegulacao(String chaveRegulacao) {
        this.chaveRegulacao = chaveRegulacao;
    }

    public String getSistemaRegulacao() {
        return sistemaRegulacao;
    }

    public void setSistemaRegulacao(String sistemaRegulacao) {
        this.sistemaRegulacao = sistemaRegulacao;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getCpfPaciente() {
        return cpfPaciente;
    }

    public void setCpfPaciente(String cpfPaciente) {
        this.cpfPaciente = cpfPaciente;
    }

    public String getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(String cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public String getCodigoIbgeOrigemPaciente() {
        return codigoIbgeOrigemPaciente;
    }

    public void setCodigoIbgeOrigemPaciente(String codigoIbgeOrigemPaciente) {
        this.codigoIbgeOrigemPaciente = codigoIbgeOrigemPaciente;
    }

    public String getMunicipioOrigemPaciente() {
        return municipioOrigemPaciente;
    }

    public void setMunicipioOrigemPaciente(String municipioOrigemPaciente) {
        this.municipioOrigemPaciente = municipioOrigemPaciente;
    }

    public String getUnidadeExecutante() {
        return unidadeExecutante;
    }

    public void setUnidadeExecutante(String unidadeExecutante) {
        this.unidadeExecutante = unidadeExecutante;
    }

    public String getCodigoIbgeUnidadeExecutante() {
        return codigoIbgeUnidadeExecutante;
    }

    public void setCodigoIbgeUnidadeExecutante(String codigoIbgeUnidadeExecutante) {
        this.codigoIbgeUnidadeExecutante = codigoIbgeUnidadeExecutante;
    }

    public String getMunicipioUnidadeExecutante() {
        return municipioUnidadeExecutante;
    }

    public void setMunicipioUnidadeExecutante(String municipioUnidadeExecutante) {
        this.municipioUnidadeExecutante = municipioUnidadeExecutante;
    }

    public String getCodigoProcedimentoSIGTAP() {
        return codigoProcedimentoSIGTAP;
    }

    public void setCodigoProcedimentoSIGTAP(String codigoProcedimentoSIGTAP) {
        this.codigoProcedimentoSIGTAP = codigoProcedimentoSIGTAP;
    }

    public String getDescricaoProcedimentoSIGTAP() {
        return descricaoProcedimentoSIGTAP;
    }

    public void setDescricaoProcedimentoSIGTAP(String descricaoProcedimentoSIGTAP) {
        this.descricaoProcedimentoSIGTAP = descricaoProcedimentoSIGTAP;
    }

    public String getCodigoProcedimentoOriginal() {
        return codigoProcedimentoOriginal;
    }

    public void setCodigoProcedimentoOriginal(String codigoProcedimentoOriginal) {
        this.codigoProcedimentoOriginal = codigoProcedimentoOriginal;
    }

    public String getDescricaoProcedimentoOriginal() {
        return descricaoProcedimentoOriginal;
    }

    public void setDescricaoProcedimentoOriginal(String descricaoProcedimentoOriginal) {
        this.descricaoProcedimentoOriginal = descricaoProcedimentoOriginal;
    }

    public String getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(String dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public String getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(String dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public String getDataFaturamento() {
        return dataFaturamento;
    }

    public void setDataFaturamento(String dataFaturamento) {
        this.dataFaturamento = dataFaturamento;
    }

    public String getProcedimentoComplementar() {
        return procedimentoComplementar;
    }

    public void setProcedimentoComplementar(String procedimentoComplementar) {
        this.procedimentoComplementar = procedimentoComplementar;
    }


    public String getCodCadSus() {
        return codCadSus;
    }

    public void setCodCadSus(String codCadSus) {
        this.codCadSus = codCadSus;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }
}