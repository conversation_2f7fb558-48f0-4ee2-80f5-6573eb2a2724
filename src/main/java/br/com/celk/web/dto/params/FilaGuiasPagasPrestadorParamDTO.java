package br.com.celk.web.dto.params;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import javax.ws.rs.QueryParam;
import java.io.Serializable;

@Schema(name="FilaGuiasPagasPrestadorParam")
public class FilaGuiasPagasPrestadorParamDTO implements Serializable {

    @QueryParam("pageNumber")
    @Positive(message = "Page Number should be greater than 0")
    @NotNull(message = "Page Number is required")
    private int pageNumber;

    @QueryParam("startDate")
    @Size(min = 7, max = 7, message = "Start date invalid (Valid format: yyyy-MM)")
    private String startDate;

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @Override
    public String toString() {
        return "ScheduleParamDTO{" +
                "pageNumber=" + pageNumber +
                "} " + super.toString();
    }
}
