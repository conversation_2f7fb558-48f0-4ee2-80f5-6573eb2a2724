package br.com.celk.web.dto;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(name = "Scheduling")
public class SchedulingDTO implements Serializable {

    @NotNull(message = "Correlation Id is required")
    private Long correlationId;

    @NotNull(message = "Patient Id is required")
    private Long patientId;

    @NotNull(message = "Care Unit Id is required")
    private Long careUnitId;

    @NotNull(message = "Grid Schedule Time Id is required")
    private Long gridScheduleTimeId;

    @NotNull(message = "Grid Schedule Service Id is required")
    private Long gridScheduleServiceId;

    @NotNull(message = "Procedure Id is required")
    private Long procedureId;

    public Long getCorrelationId() {
        return correlationId;
    }

    public void setCorrelationId(Long correlationId) {
        this.correlationId = correlationId;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public Long getCareUnitId() {
        return careUnitId;
    }

    public void setCareUnitId(Long careUnitId) {
        this.careUnitId = careUnitId;
    }

    public Long getGridScheduleTimeId() {
        return gridScheduleTimeId;
    }

    public void setGridScheduleTimeId(Long gridScheduleTimeId) {
        this.gridScheduleTimeId = gridScheduleTimeId;
    }

    public Long getGridScheduleServiceId() {
        return gridScheduleServiceId;
    }

    public void setGridScheduleServiceId(Long gridScheduleServiceId) {
        this.gridScheduleServiceId = gridScheduleServiceId;
    }

    public Long getProcedureId() {
        return procedureId;
    }

    public void setProcedureId(Long procedureId) {
        this.procedureId = procedureId;
    }

    @Override
    public String toString() {
        return "SchedulingDTO{" +
                "correlationId=" + correlationId +
                ", patientId=" + patientId +
                ", careUnitId=" + careUnitId +
                ", gridScheduleTimeId=" + gridScheduleTimeId +
                ", gridScheduleServiceId=" + gridScheduleServiceId +
                ", procedureId=" + procedureId +
                '}';
    }
}
