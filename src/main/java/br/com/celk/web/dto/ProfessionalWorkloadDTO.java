package br.com.celk.web.dto;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;

@Schema(name="ProfessionalWorkload", description = "Model with Professional work related information")
public class ProfessionalWorkloadDTO implements Serializable {

    private Long professionalWorkloadId;
    private Long careUnitId;
    private String professionalSpecialtyId;
    private String professionalRegistrationId;
    private Long professionalId;

    public Long getProfessionalWorkloadId() {
        return professionalWorkloadId;
    }

    public void setProfessionalWorkloadId(Long professionalWorkloadId) {
        this.professionalWorkloadId = professionalWorkloadId;
    }

    public Long getCareUnitId() {
        return careUnitId;
    }

    public void setCareUnitId(Long careUnitId) {
        this.careUnitId = careUnitId;
    }

    public String getProfessionalSpecialtyId() {
        return professionalSpecialtyId;
    }

    public void setProfessionalSpecialtyId(String professionalSpecialtyId) {
        this.professionalSpecialtyId = professionalSpecialtyId;
    }

    public String getProfessionalRegistrationId() {
        return professionalRegistrationId;
    }

    public void setProfessionalRegistrationId(String professionalRegistrationId) {
        this.professionalRegistrationId = professionalRegistrationId;
    }

    public Long getProfessionalId() {
        return professionalId;
    }

    public void setProfessionalId(Long professionalId) {
        this.professionalId = professionalId;
    }

    @Override
    public String toString() {
        return "ProfessionalWorkloadDTO{" +
                "professionalWorkloadId=" + professionalWorkloadId +
                ", careUnitId=" + careUnitId +
                ", professionalSpecialtyId='" + professionalSpecialtyId + '\'' +
                ", professionalRegistrationId='" + professionalRegistrationId + '\'' +
                ", professionalId=" + professionalId +
                '}';
    }
}
