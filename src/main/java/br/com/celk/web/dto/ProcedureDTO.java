package br.com.celk.web.dto;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;

@Schema(name="Procedure")
public class ProcedureDTO implements Serializable {

    private Long procedureId;
    private String procedureName;

    public Long getProcedureId() {
        return procedureId;
    }

    public void setProcedureId(Long procedureId) {
        this.procedureId = procedureId;
    }

    public String getProcedureName() {
        return procedureName;
    }

    public void setProcedureName(String procedureName) {
        this.procedureName = procedureName;
    }

    @Override
    public String toString() {
        return "ProcedureDTO{" +
                "procedureId=" + procedureId +
                ", procedureName='" + procedureName + '\'' +
                '}';
    }
}
