package br.com.celk.web.dto;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;

@Schema(name="Schedule")
public class ScheduleDTO implements Serializable {

    private Long scheduleId;
    private Long careUnitId;
    private Long professionalId;
    private Long procedureId;

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Long getProfessionalId() {
        return professionalId;
    }

    public void setProfessionalId(Long professionalId) {
        this.professionalId = professionalId;
    }

    public Long getProcedureId() {
        return procedureId;
    }

    public void setProcedureId(Long procedureId) {
        this.procedureId = procedureId;
    }

    public Long getCareUnitId() {
        return careUnitId;
    }

    public void setCareUnitId(Long careUnitId) {
        this.careUnitId = careUnitId;
    }

    @Override
    public String toString() {
        return "ScheduleDTO{" +
                "scheduleId=" + scheduleId +
                ", careUnitId=" + careUnitId +
                ", professionalId=" + professionalId +
                ", procedureId=" + procedureId +
                '}';
    }
}
