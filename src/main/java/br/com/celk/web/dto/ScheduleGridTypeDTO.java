package br.com.celk.web.dto;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;

@Schema(name="ScheduleGridType")
public class ScheduleGridTypeDTO implements Serializable {

    private Long scheduleGridTypeId;
    private String description;
    private Long type;

    public Long getScheduleGridTypeId() {
        return scheduleGridTypeId;
    }

    public void setScheduleGridTypeId(Long scheduleGridTypeId) {
        this.scheduleGridTypeId = scheduleGridTypeId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "ScheduleGridTypeDTO{" +
                "scheduleGridTypeId=" + scheduleGridTypeId +
                ", description='" + description + '\'' +
                ", type=" + type +
                '}';
    }
}
