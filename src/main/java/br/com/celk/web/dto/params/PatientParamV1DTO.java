package br.com.celk.web.dto.params;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.validation.constraints.PositiveOrZero;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.QueryParam;

@Schema(name="PatientParamV1")
public class PatientParamV1DTO extends PatientCommonParamDTO {

    private Integer listPatientsLimit = 50;

    @QueryParam("offset")
    @DefaultValue("0")
    @PositiveOrZero(message = "Offset must be a integer equal or greater than 0")
    private String offset;

    public Integer getListPatientsLimit() {
        return listPatientsLimit;
    }

    public void setListPatientsLimit(Integer listPatientsLimit) {
        this.listPatientsLimit = listPatientsLimit;
    }

    public String getOffset() {
        return this.offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    @Override
    public String toString() {
        return "PatientParamV1DTO{" +
                "listPatientsLimit=" + listPatientsLimit +
                ", offset='" + offset + '\'' +
                "} " + super.toString();
    }
}
