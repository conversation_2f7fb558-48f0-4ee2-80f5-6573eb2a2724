package br.com.celk.web.dto;


import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;

@Schema(name = "SchedulingResponse")
public class SchedulingResponseDTO extends ClkBatchGeneric implements Serializable {

    private Long schedulingId;

    public Long getSchedulingId() {
        return schedulingId;
    }

    public void setSchedulingId(Long schedulingId) {
        this.schedulingId = schedulingId;
    }

    @Override
    public String toString() {
        return "SchedulingResponseDTO{" +
                "schedulingId=" + schedulingId +
                "} " + super.toString();
    }
}
