package br.com.celk.web.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(name = "FilaAgendamento")
@JsonInclude()
public class FilaAgendamentoDTO implements Serializable {

    private Long idSolicitacao;
    private String tipoProcedimento;
    private List<ProcedimentoDTO> itensProcedimentos;
    private String tipoConsulta;
    private String situacao;
    private String posicao;
    private Long codPaciente;
    private String cns;
    private String paciente;
    private String sexo;
    private String dataNasc;
    private String nomeMae;
    private String cidade;
    private String prioridade;
    private String obsPriorizacao;
    private String dataSolicitacaoAgendamento;


    public Long getIdSolicitacao() {
        return idSolicitacao;
    }

    public void setIdSolicitacao(Long idSolicitacao) {
        this.idSolicitacao = idSolicitacao;
    }

    public String getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(String tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public List<ProcedimentoDTO> getItensProcedimentos() {
        return itensProcedimentos;
    }

    public void setItensProcedimentos(List<ProcedimentoDTO> itensProcedimentos) {
        this.itensProcedimentos = itensProcedimentos;
    }

    public String getTipoConsulta() {
        return tipoConsulta;
    }

    public void setTipoConsulta(String tipoConsulta) {
        this.tipoConsulta = tipoConsulta;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getPosicao() {
        return posicao;
    }

    public void setPosicao(String posicao) {
        this.posicao = posicao;
    }

    public Long getCodPaciente() {
        return codPaciente;
    }

    public void setCodPaciente(Long codPaciente) {
        this.codPaciente = codPaciente;
    }

    public String getCns() {
        return cns;
    }

    public void setCns(String cns) {
        this.cns = cns;
    }

    public String getPaciente() {
        return paciente;
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getDataNasc() {
        return dataNasc;
    }

    public void setDataNasc(String dataNasc) {
        this.dataNasc = dataNasc;
    }

    public String getNomeMae() {
        return nomeMae;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getPrioridade() {
        return prioridade;
    }

    public void setPrioridade(String prioridade) {
        this.prioridade = prioridade;
    }

    public String getObsPriorizacao() {
        return obsPriorizacao;
    }

    public void setObsPriorizacao(String obsPriorizacao) {
        this.obsPriorizacao = obsPriorizacao;
    }

    public String getDataSolicitacaoAgendamento() {
        return dataSolicitacaoAgendamento;
    }

    public void setDataSolicitacaoAgendamento(String dataSolicitacaoAgendamento) {
        this.dataSolicitacaoAgendamento = dataSolicitacaoAgendamento;
    }
}