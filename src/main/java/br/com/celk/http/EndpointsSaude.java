package br.com.celk.http;

public enum EndpointsSaude {
    CONSULTA_PACIENTE("paciente/1.1/consultar?cpf={cpf}&cns={cns}"),
    CONSULTA_PRONTUARIOS("atendimentoProntuario/1.0/000000/consultarProntuarios/{revisao}?cpf={cpf}&cns={cns}"),
    SALVAR_PRONTUARIO("atendimentoProntuario/1.0/000000/salvar"),
    ENVIAR_RETORNO_PSC("assinatura/v1/api/psc-callback?state={state}&code={code}"),
    GRUPOS_VACINACAO("vacina/gruposVacinacao"),
    PACIENTE_VACINAS("vacina/{idPaciente}"),
    PACIENTE_CADERNETA_VACINACAO("vacina/{idPaciente}/caderneta"),
    PACIENTE_CALENDARIO_VACINACAO("vacina/{idPaciente}/calendario/{faixaEtaria}"),
    PACIENTE_FAIXAS_ETARIAS("vacina/{idPaciente}/faixa-etaria"),
    PACIENTE("paciente/{idPaciente}"),
    PACIENTE_MEDICAMENTOS_CONTINUOS("paciente/{idPaciente}/medicamentoContinuo"),
    PACIENTE_EXAME("paciente/{idPaciente}/exame"),
    PACIENTE_AGENDAMENTOS("paciente/{idPaciente}/agendamentos"),
    PACIENTE_CONTATO("paciente/contato"),
    PACIENTE_LIBERAR_CONTATO("paciente/{idPaciente}/liberar"),
    PACIENTE_DADOS_VACINACAO("paciente/{idPaciente}/dadosVacinacao"),

    AGENDA_PRODECIMENTO_LIST("agenda/procedimento"),
    AGENDA_HORARIOS("agenda"),
    AGENDA_AGENDAR_PACIENTE("agendamento/municipio/{idMunicipio}"),
    AGENDA_CANCELAR_AGENDAMENTO("agendamento/municipio/{idMunicipio}/cancelar/{idAgenda}?reabrirSolicitacao={reabrirSolicitacao}&motivoCancelamento={motivoCancelamento}"),
    AGENDA_REGISTRAR_OCORRENCIA("agenda/registrarOcorrencia"),
    COMPROVANTE_AGENDAMENTO("/agenda/comprovanteAgendamento"),

    // V2
    PACIENTE_MEDICAMENTOS_CONTINUOS_V2("paciente/medicamentoContinuo"),
    PACIENTE_EXAME_V2("paciente/exame"),
    PACIENTE_AGENDAMENTOS_V2("paciente/agendamentos"),
    PACIENTE_VACINAS_V2("paciente/vacina"),
    ;

    private String endpoint;

    EndpointsSaude(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getEndpoint() {
        return endpoint;
    }
}