package br.com.celk.http;

import br.com.celk.config.ProfileConfig;
import br.com.celk.service.DynamoDynamoDbConfigService;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

@ApplicationScoped
public class SaudeEndpointProvider {
    private static final Logger LOG = Logger.getLogger(SaudeEndpointProvider.class.getName());

    @Inject
    ProfileConfig profileConfig;

    @Inject
    DynamoDynamoDbConfigService dynamoDbConfigService;

    @ConfigProperty(name = "celk.saude.url")
    String url;

    public SaudeEndpointProvider() {
        LOG.info("Conectando com CS...");
    }

    public String getEndpointConsultarPaciente(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.REST_BASE.getTarget() + EndpointsSaude.CONSULTA_PACIENTE.getEndpoint();
    }

    public String getEndpointGruposVacinacao(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.GRUPOS_VACINACAO.getEndpoint();
    }

    public String getEndpointVacinasPaciente(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO_V2.getTarget() + EndpointsSaude.PACIENTE_VACINAS_V2.getEndpoint();
    }

    public String getEndpointCadernetaVacinacaoPaciente(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.PACIENTE_CADERNETA_VACINACAO.getEndpoint();
    }

    public String getEndpointCalendarioVacinacaoPaciente(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.PACIENTE_CALENDARIO_VACINACAO.getEndpoint();
    }

    public String getEndpointFaixasEtariasPaciente(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.PACIENTE_FAIXAS_ETARIAS.getEndpoint();
    }

    public String getEndpointPaciente(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.PACIENTE.getEndpoint();
    }

    public String getEndpointPacienteMedicamentosContinuos(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO_V2.getTarget() + EndpointsSaude.PACIENTE_MEDICAMENTOS_CONTINUOS_V2.getEndpoint();
    }

    public String getEndpointPacienteExame(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO_V2.getTarget() + EndpointsSaude.PACIENTE_EXAME_V2.getEndpoint();
    }

    public String getEndpointPacienteAgendamentos(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO_V2.getTarget() + EndpointsSaude.PACIENTE_AGENDAMENTOS_V2.getEndpoint();
    }

    public String getEndpointPacienteContato(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.PACIENTE_CONTATO.getEndpoint();
    }

    public String getEndpointPacienteLiberarContato(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.PACIENTE_LIBERAR_CONTATO.getEndpoint();
    }

    public String getEndpointPacienteDadosVacinacao(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.PACIENTE_DADOS_VACINACAO.getEndpoint();
    }

    public String getEndpointAgendaProcedimentoList(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.AGENDA_PRODECIMENTO_LIST.getEndpoint();
    }

    public String getEndpointComprovanteAgendamento(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.COMPROVANTE_AGENDAMENTO.getEndpoint();
    }

    public String getEndpointAgendaHorarios(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.AGENDA_HORARIOS.getEndpoint();
    }

    public String getEndpointAgendaAgendarPaciente(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.AGENDA_AGENDAR_PACIENTE.getEndpoint();
    }

    public String getEndpointRegistraOcorrencia(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.AGENDA_REGISTRAR_OCORRENCIA.getEndpoint();
    }

    public String getEndpointAgendaCancelarAgendamento(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.APP_CIDADAO.getTarget() + EndpointsSaude.AGENDA_CANCELAR_AGENDAMENTO.getEndpoint();
    }

    public String listProntuariosByPaciente(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.REST_BASE.getTarget() + EndpointsSaude.CONSULTA_PRONTUARIOS.getEndpoint();
    }

    public String saveProntuario(String tenant) {
        return getBaseURLSaude(tenant) + SaudeURLTargets.REST_BASE.getTarget() + EndpointsSaude.SALVAR_PRONTUARIO.getEndpoint();
    }

    public String enviarRetornoPsc(String tenant, String code, String state) {
        String endpoint = EndpointsSaude.ENVIAR_RETORNO_PSC.getEndpoint();
        endpoint = endpoint.replace("{code}", code);
        endpoint = endpoint.replace("{state}", state);

        return getBaseURLSaude(tenant) + SaudeURLTargets.REST_BASE.getTarget() + endpoint;
    }

    private String getBaseURLSaude(String tenant) {
        String tenantName = tenant;

        String finalUrl = url.replace("tenant:8080", tenantName);

        if (tenantName.equalsIgnoreCase("localhost")) {
            finalUrl = url.replace("tenant", tenantName);
            finalUrl = finalUrl.replace(".celk.com.br", "");
        }

        // Para usar ambiente de squad, definir o tenant iniciando com "squad". Ex.: squadberlim
        if (tenantName.startsWith("squad")) {
            tenantName = tenant.replace("squad", "");
            finalUrl = url.replace("tenant:8080", tenantName);
            finalUrl = finalUrl.replace(".celk.com.br", ".squads.celk.info");
        }

        if (!finalUrl.endsWith("/")) {
            finalUrl = finalUrl.concat("/");
        }

        if (finalUrl.contains("squads.celk.info.celk.com.br")) {
            finalUrl = finalUrl.replace(".celk.com.br","");
        }

        return finalUrl;
    }

}