package br.com.celk.utils;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.jboss.logging.Logger;

public class JsonUtils {

    private static final Logger LOG = Logger.getLogger(JsonUtils.class.getName());

    public static String toJson(Object obj) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
            mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static Object toObject(String content, Class<?> toObject) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(content, toObject);
        } catch (JsonProcessingException e) {
            LOG.error(e.getMessage());
        }
        return null;
    }

}
