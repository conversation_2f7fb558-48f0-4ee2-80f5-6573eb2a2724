package br.com.celk.utils.json;

import javax.json.bind.adapter.JsonbAdapter;

public class LongAdapter implements JsonbAdapter<Long, String> {
    @Override
    public String adaptToJson(Long obj) throws Exception {
        return obj == null ? null : obj.toString();
    }

    @Override
    public Long adaptFromJson(String obj) throws Exception {
        if (obj == null || obj.trim().isEmpty()) {
            return null;
        }
        return Long.valueOf(obj);
    }
}