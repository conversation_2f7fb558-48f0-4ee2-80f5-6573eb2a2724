package br.com.celk.utils.webservice;

import org.jboss.logging.Logger;

import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;

public class RestUtil {

    private static final Logger LOG = Logger.getLogger(RestUtil.class);

    public static MultivaluedMap<String, Object> getHeaders() {
        final String id = "87c98f29-4525-4a1c-91fb-ad0a7f3b0679";
        final String chave = "8423b2a3-9e16-40c7-8c3b-4aac94c862a2";
        long data = new Date().getTime();

        MultivaluedMap<String, Object> headers = new MultivaluedHashMap<>();
        headers.add("id", id);
        headers.add("data", Long.toString(data));
        String sha256 = criptoHash(id + String.valueOf(data) + chave, null);
        headers.add("hash", sha256);
        return headers;
    }

    private static String criptoHash(String senhaOriginal, String method) {
        try {
            MessageDigest algorithm;
            if (method == null) {
                algorithm = MessageDigest.getInstance("SHA-256");
            } else {
                algorithm = MessageDigest.getInstance(method);
            }
            byte[] messageDigest = algorithm.digest(senhaOriginal.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                hexString.append(String.format("%02X", 0xFF & b));
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException ex) {
            LOG.error(ex.getMessage(), ex);
        }
        return "";
    }

}
