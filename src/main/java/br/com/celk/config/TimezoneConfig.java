package br.com.celk.config;

import br.com.celk.config.tenant.CustomTenantResolver;
import io.quarkus.runtime.StartupEvent;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.event.Observes;
import java.time.ZonedDateTime;
import java.util.TimeZone;

@ApplicationScoped
public class TimezoneConfig {

    private static final Logger LOG = Logger.getLogger(CustomTenantResolver.class.getName());

    void onStart(@Observes StartupEvent ev) {
        TimeZone.setDefault(TimeZone.getTimeZone("America/Sao_Paulo"));
        LOG.info("Timezone set to: " + TimeZone.getDefault().getID());
        LOG.info("Timezone atual: " + TimeZone.getDefault().getID() + " | Hora atual: " + ZonedDateTime.now());
    }
}
