package br.com.celk.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "celk.keycloak")
public class KeycloakConfig {

    private String userRealm;
    private String clientCredentials;
    private String clientCredentialsSecret;

    public String getUserRealm() {
        return this.userRealm;
    }

    public void setUserRealm(String userRealm) {
        this.userRealm = userRealm;
    }

    public String getClientCredentials() {
        return this.clientCredentials;
    }

    public void setClientCredentials(String clientCredentials) {
        this.clientCredentials = clientCredentials;
    }

    public String getClientCredentialsSecret() {
        return this.clientCredentialsSecret;
    }

    public void setClientCredentialsSecret(String clientCredentialsSecret) {
        this.clientCredentialsSecret = clientCredentialsSecret;
    }
}
