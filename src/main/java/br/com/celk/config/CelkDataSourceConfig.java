package br.com.celk.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "celk.datasource.config")
public class CelkDataSourceConfig {

    private Boolean metrics;
    private Integer minSize;
    private Integer maxSize;
    private Integer maxLifetime;;
    private Integer initialSize;
    private Integer acquisitionTimeout;
    private Integer leakTimeout;
    private Integer validationTimeout;
    private Integer idleValidationTimeout;
    private Integer reapTimeout;
    private Boolean autoCommit;
    private String driver;

    public Boolean getMetrics() {
        return this.metrics;
    }

    public void setMetrics(Boolean metrics) {
        this.metrics = metrics;
    }

    public Integer getMinSize() {
        return this.minSize;
    }

    public void setMinSize(Integer minSize) {
        this.minSize = minSize;
    }

    public Integer getMaxSize() {
        return this.maxSize;
    }

    public void setMaxSize(Integer maxSize) {
        this.maxSize = maxSize;
    }

    public Integer getInitialSize() {
        return this.initialSize;
    }

    public void setInitialSize(Integer initialSize) {
        this.initialSize = initialSize;
    }

    public Integer getAcquisitionTimeout() {
        return this.acquisitionTimeout;
    }

    public void setAcquisitionTimeout(Integer acquisitionTimeout) {
        this.acquisitionTimeout = acquisitionTimeout;
    }

    public Integer getLeakTimeout() {
        return this.leakTimeout;
    }

    public void setLeakTimeout(Integer leakTimeout) {
        this.leakTimeout = leakTimeout;
    }

    public Integer getValidationTimeout() {
        return this.validationTimeout;
    }

    public void setValidationTimeout(Integer validationTimeout) {
        this.validationTimeout = validationTimeout;
    }

    public Integer getIdleValidationTimeout() {
        return idleValidationTimeout;
    }

    public void setIdleValidationTimeout(Integer idleValidationTimeout) {
        this.idleValidationTimeout = idleValidationTimeout;
    }

    public Integer getReapTimeout() {
        return this.reapTimeout;
    }

    public void setReapTimeout(Integer reapTimeout) {
        this.reapTimeout = reapTimeout;
    }

    public Boolean getAutoCommit() {
        return this.autoCommit;
    }

    public void setAutoCommit(Boolean autoCommit) {
        this.autoCommit = autoCommit;
    }

    public String getDriver() {
        return this.driver;
    }

    public void setDriver(String driver) {
        this.driver = driver;
    }

    public Integer getMaxLifetime() {
        return maxLifetime;
    }

    public void setMaxLifetime(Integer maxLifetime) {
        this.maxLifetime = maxLifetime;
    }
}
