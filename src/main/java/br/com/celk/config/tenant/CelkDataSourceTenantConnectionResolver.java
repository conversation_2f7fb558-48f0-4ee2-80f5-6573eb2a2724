package br.com.celk.config.tenant;

import br.com.celk.config.DataSourcesConfig;
import io.agroal.api.AgroalDataSource;
import io.agroal.api.configuration.AgroalDataSourceConfiguration;
import io.quarkus.agroal.DataSource;
import io.quarkus.arc.Arc;
import io.quarkus.arc.Unremovable;
import io.quarkus.hibernate.orm.runtime.customized.QuarkusConnectionProvider;
import io.quarkus.hibernate.orm.runtime.tenant.TenantConnectionResolver;
import org.hibernate.engine.jdbc.connections.spi.ConnectionProvider;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.sql.SQLException;


@Unremovable
@ApplicationScoped
public class CelkDataSourceTenantConnectionResolver implements TenantConnectionResolver {

    private static final Logger LOG = Logger.getLogger(CelkDataSourceTenantConnectionResolver.class);

    @Inject
    DataSourcesConfig dataSourcesConfig;
    @Inject
    RequestContext requestContext;

    private static AgroalDataSource create(AgroalDataSourceConfiguration config) {
        try {
            return AgroalDataSource.from(config);
        } catch (SQLException ex) {
            throw new IllegalStateException("Failed to create a new data source based on the default config", ex);
        }
    }

    @Override
    public ConnectionProvider resolve(String tenantId) {
        LOG.info("Resolver ::tenant - " + tenantId);
        String method = requestContext.getRequestMethod();
        LOG.info("HTTP Method: " + method);
        boolean isReadOnly = "GET".equalsIgnoreCase(method);
        AgroalDataSource dataSource = tenantDataSource(tenantId);

        if (dataSource == null) {
            if (requestContext.isUsaConexaoDiretaComBanco()) {
                if (isReadOnly) {
                    dataSource = create(dataSourcesConfig.getDataSourceConfigLeitura(tenantId));
                } else {
                    dataSource = create(dataSourcesConfig.getDataSourceConfigEscrita(tenantId));
                }

                if (dataSource == null) {
                    LOG.fatal("No instance of datasource found for tenant: " + tenantId);
                    throw new IllegalStateException("No instance of datasource found for tenant: " + tenantId);
                }
            }
        }
        return new QuarkusConnectionProvider(dataSource);
    }

    private static AgroalDataSource tenantDataSource(String tenantId) {
        return Arc.container().instance(AgroalDataSource.class, new DataSource.DataSourceLiteral(tenantId)).get();
    }

}
