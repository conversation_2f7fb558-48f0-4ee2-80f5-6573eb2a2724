package br.com.celk.config.tenant;

public class TenantContext {

    public static ThreadLocal<String> currentTenant = new ThreadLocal<>();

    public static void setCurrentTenant(String tenant) {
        currentTenant.set(tenant);
    }

    public static String getCurrentTenant() {
        return currentTenant.get() == null ? "" : currentTenant.get();
    }

    public static void clear() {
        currentTenant.remove();
    }
}
