package br.com.celk.config.tenant;

import io.quarkus.arc.Unremovable;
import io.quarkus.hibernate.orm.runtime.tenant.TenantResolver;
import io.quarkus.oidc.runtime.OidcJwtCallerPrincipal;
import io.quarkus.security.identity.SecurityIdentity;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;

import javax.annotation.Priority;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.security.Principal;
import java.util.Optional;

@Unremovable
@RequestScoped
@Priority(1)
public class CustomTenantResolver implements TenantResolver {
    private static final Logger LOG = Logger.getLogger(CustomTenantResolver.class.getName());

    @Inject
    SecurityIdentity securityIdentity;

    @Inject
    RoutingContext context;

    @Override
    public String getDefaultTenantId() {
        return "master";
    }

    @Override
    public String resolveTenantId() {
        if (this.securityIdentity.isAnonymous()) {
            throw new IllegalArgumentException("token not found");
        }
        String tenantName = getTenant();
        TenantContext.setCurrentTenant(tenantName.toLowerCase());

        return tenantName;
    }

    private String getTenant() {
        String tenantName = getTenantByKeycloak();

        if (!StringUtils.isEmpty(tenantName)) {
            LOG.info("Tenant obtido via keycloak  ::tenant_name - ".concat(tenantName));
            return tenantName;
        } else {
            tenantName = context.request().headers().get("X-TenantID");
            if (!StringUtils.isEmpty(tenantName)) {
                LOG.info("Tenant obtido via header ::X-TenantID - ".concat(tenantName));
                return tenantName;
            }
            throw new IllegalStateException("Tenant não definido para o cliente.");
        }
    }

    private String getTenantByKeycloak() {
        // Tenant registrado junto ao usuário no keycloak
        Principal principal = this.securityIdentity.getPrincipal();
        Optional<String> tenantNameOpt = ((OidcJwtCallerPrincipal) principal).claim("tenant_name");

        return tenantNameOpt.orElse(null);
    }


}
