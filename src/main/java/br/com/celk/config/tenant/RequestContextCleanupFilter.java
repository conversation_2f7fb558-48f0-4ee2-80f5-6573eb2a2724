package br.com.celk.config.tenant;

import javax.inject.Inject;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerResponseContext;
import javax.ws.rs.container.ContainerResponseFilter;

public class RequestContextCleanupFilter implements ContainerResponseFilter {

    @Inject
    RequestContext requestContext;

    @Override
    public void filter(ContainerRequestContext requestContext, ContainerResponseContext responseContext) {
        this.requestContext.clear();
    }

}
