package br.com.celk.config;

import io.quarkus.panache.common.Parameters;
import org.eclipse.microprofile.config.ConfigProvider;
import org.keycloak.authorization.client.AuthzClient;
import org.keycloak.authorization.client.Configuration;
import org.keycloak.representations.AccessTokenResponse;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.client.ClientRequestFilter;
import javax.ws.rs.core.MultivaluedMap;
import java.time.LocalDateTime;

@ApplicationScoped
public class AuthClient {

    static final String URL = ConfigProvider.getConfig().getValue("quarkus.oidc.auth-server-url", String.class);

    @Inject
    KeycloakConfig keycloakConfig;

    private static AccessTokenResponse accessTokenResponse;
    private static AuthzClient authzClient;
    private static LocalDateTime expiresIn;

    @PostConstruct
    synchronized void init() {
        Configuration configuration = new Configuration(
                URL.split("realms/")[0],
                keycloakConfig.getUserRealm(),
                this.keycloakConfig.getClientCredentials(),
                Parameters.with("secret", this.keycloakConfig.getClientCredentialsSecret()).map(),
                null
        );

        authzClient = AuthzClient.create(configuration);
        accessTokenResponse = AuthzClient.create(configuration).obtainAccessToken();
        expiresIn = LocalDateTime.now().plusSeconds(accessTokenResponse.getExpiresIn() - 15);
    }

    public synchronized ClientRequestFilter getCredentials() {
        return clientRequestContext -> {
            MultivaluedMap<String, Object> headers = clientRequestContext.getHeaders();
            headers.add("Authorization", "Bearer " + this.getAccessTokenResponse().getToken());
        };
    }

    private synchronized AccessTokenResponse getAccessTokenResponse() {
        if (expiresIn.isBefore(LocalDateTime.now())) {
            accessTokenResponse = authzClient.obtainAccessToken();
            expiresIn = LocalDateTime.now().plusSeconds(accessTokenResponse.getExpiresIn() - 15);
        }
        return accessTokenResponse;
    }
}