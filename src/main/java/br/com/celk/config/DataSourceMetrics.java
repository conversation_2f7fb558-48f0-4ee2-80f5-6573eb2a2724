package br.com.celk.config;

import br.com.celk.repository.FilaAgendamentosRepository;
import io.agroal.api.AgroalDataSource;
import io.quarkus.runtime.StartupEvent;
import org.eclipse.microprofile.metrics.Gauge;
import org.eclipse.microprofile.metrics.MetricRegistry;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.event.Observes;
import javax.inject.Inject;

@ApplicationScoped
public class DataSourceMetrics {

    private static final Logger LOG = Logger.getLogger(FilaAgendamentosRepository.class);

    @Inject
    AgroalDataSource defaultDataSource;

    @Inject
    MetricRegistry registry;

    void init(@Observes StartupEvent ev) {
        registry.register("db_active_connections", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Active Count: " + defaultDataSource.getMetrics().activeCount());
                return defaultDataSource.getMetrics().activeCount();
            }
        });
        registry.register("db_max_connections", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Max Connections: " + defaultDataSource.getMetrics().maxUsedCount());
                return defaultDataSource.getMetrics().maxUsedCount();
            }
        });
        registry.register("db_available_connections", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Available Connections: " + defaultDataSource.getMetrics().availableCount());
                return defaultDataSource.getMetrics().availableCount();
            }
        });
        registry.register("db_reap_count", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Reap Count: " + defaultDataSource.getMetrics().reapCount());
                return defaultDataSource.getMetrics().reapCount();
            }
        });
        registry.register("db_acquire_count", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Acquire Count: " + defaultDataSource.getMetrics().acquireCount());
                return defaultDataSource.getMetrics().acquireCount();
            }
        });
        registry.register("db_creation_count", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Creation Count: " + defaultDataSource.getMetrics().creationCount());
                return defaultDataSource.getMetrics().creationCount();
            }
        });
        registry.register("db_awaiting_count", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Awaiting Count: " + defaultDataSource.getMetrics().awaitingCount());
                return defaultDataSource.getMetrics().awaitingCount();
            }
        });
        registry.register("db_destroy_count", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Destroy Count: " + defaultDataSource.getMetrics().destroyCount());
                return defaultDataSource.getMetrics().destroyCount();
            }
        });
        registry.register("db_flush_count", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Flush Count: " + defaultDataSource.getMetrics().flushCount());
                return defaultDataSource.getMetrics().flushCount();
            }
        });
        registry.register("db_invalid_count", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Invalid Count: " + defaultDataSource.getMetrics().invalidCount());
                return defaultDataSource.getMetrics().invalidCount();
            }
        });
        registry.register("db_leak_detection_count", new Gauge<Long>() {
            @Override
            public Long getValue() {
                LOG.info("DB Metrics :: Leak Detection Count: " + defaultDataSource.getMetrics().leakDetectionCount());
                return defaultDataSource.getMetrics().leakDetectionCount();
            }
        });
        registry.register("db_blocking_time_avg", new Gauge<Number>() {
            @Override
            public Number getValue() {
                LOG.info("DB Metrics :: Blocking Time Avg (seconds): " + defaultDataSource.getMetrics().blockingTimeAverage().getSeconds());
                return defaultDataSource.getMetrics().blockingTimeAverage().getSeconds();
            }
        });
        registry.register("db_blocking_time_max", new Gauge<Number>() {
            @Override
            public Number getValue() {
                LOG.info("DB Metrics :: Blocking Time Max (seconds): " + defaultDataSource.getMetrics().blockingTimeMax().getSeconds());
                return defaultDataSource.getMetrics().blockingTimeMax().getSeconds();
            }
        });
        registry.register("db_blocking_time_total", new Gauge<Number>() {
            @Override
            public Number getValue() {
                LOG.info("DB Metrics :: Blocking Time Total (seconds): " + defaultDataSource.getMetrics().blockingTimeTotal().getSeconds());
                return defaultDataSource.getMetrics().blockingTimeTotal().getSeconds();
            }
        });
        registry.register("db_creation_time_avg", new Gauge<Number>() {
            @Override
            public Number getValue() {
                LOG.info("DB Metrics :: Creation Time Avg (seconds): " + defaultDataSource.getMetrics().creationTimeAverage().getSeconds());
                return defaultDataSource.getMetrics().creationTimeAverage().getSeconds();
            }
        });
        registry.register("db_creation_time_max", new Gauge<Number>() {
            @Override
            public Number getValue() {
                LOG.info("DB Metrics :: Creation Time Max (seconds): " + defaultDataSource.getMetrics().creationTimeMax().getSeconds());
                return defaultDataSource.getMetrics().creationTimeMax().getSeconds();
            }
        });
        registry.register("db_creation_time_total", new Gauge<Number>() {
            @Override
            public Number getValue() {
                LOG.info("DB Metrics :: Creation Time Total (seconds): " + defaultDataSource.getMetrics().creationTimeTotal().getSeconds());
                return defaultDataSource.getMetrics().creationTimeTotal().getSeconds();
            }
        });
    }
}

