package br.com.celk.config;

import br.com.celk.config.tenant.CustomTenantResolver;
import br.com.celk.config.tenant.TenantContext;
import br.com.celk.service.DynamoDynamoDbConfigService;
import io.agroal.api.configuration.AgroalDataSourceConfiguration;
import io.agroal.api.configuration.supplier.AgroalDataSourceConfigurationSupplier;
import io.agroal.api.security.NamePrincipal;
import io.agroal.api.security.SimplePassword;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import static io.agroal.api.configuration.AgroalConnectionFactoryConfiguration.TransactionIsolation.READ_COMMITTED;
import static io.agroal.api.configuration.AgroalConnectionPoolConfiguration.ConnectionValidator.defaultValidator;

@ApplicationScoped
public class DataSourcesConfig {
    protected static final Map<String, CelkConfig> datasourcesConfigDynamoCache = new HashMap<>();

    @Inject
    DynamoDynamoDbConfigService dynamoDbConfigService;
    @Inject
    CelkDataSourceConfig celkDataSourceConfig;
    @Inject
    CustomTenantResolver customTenantResolver;

    /**
     * Create a new data source from tenant ID.
     *
     * @param tenantName Configuration to tenant.
     * @return New data source instance.
     */
    public AgroalDataSourceConfiguration getDataSourceConfigEscrita(String tenantName) {
        CelkConfig celkConfig = this.getDbConfig(tenantName);
        AgroalDataSourceConfigurationSupplier configuration = new AgroalDataSourceConfigurationSupplier()
                .dataSourceImplementation(AgroalDataSourceConfiguration.DataSourceImplementation.AGROAL)
                .metricsEnabled(celkDataSourceConfig.getMetrics())
                .connectionPoolConfiguration(cp -> cp
                        .minSize(celkDataSourceConfig.getMinSize())
                        .maxSize(celkDataSourceConfig.getMaxSize())
                        .maxLifetime(Duration.ofSeconds(celkDataSourceConfig.getMaxLifetime()))
                        .initialSize(celkDataSourceConfig.getInitialSize())
                        .connectionValidator(defaultValidator())
                        .acquisitionTimeout(Duration.ofSeconds(celkDataSourceConfig.getAcquisitionTimeout()))
                        .leakTimeout(Duration.ofSeconds(celkDataSourceConfig.getLeakTimeout()))
                        .validationTimeout(Duration.ofSeconds(celkDataSourceConfig.getValidationTimeout()))
                        .idleValidationTimeout(Duration.ofSeconds(celkDataSourceConfig.getIdleValidationTimeout()))
                        .reapTimeout(Duration.ofSeconds(celkDataSourceConfig.getReapTimeout()))
                        .connectionFactoryConfiguration(cf -> cf
                                .jdbcUrl(celkConfig.getUrlEscrita()+"?ApplicationName=PDA")
                                .connectionProviderClassName(celkDataSourceConfig.getDriver())
                                .autoCommit(celkDataSourceConfig.getAutoCommit())
                                .jdbcTransactionIsolation(READ_COMMITTED)
                                .principal(new NamePrincipal(celkConfig.getUser()))
                                .credential(new SimplePassword(celkConfig.getPass()))
                        )
                );
        return configuration.get();
    }

    public AgroalDataSourceConfiguration getDataSourceConfigLeitura(String tenantName) {
        CelkConfig celkConfig = this.getDbConfig(tenantName);
        AgroalDataSourceConfigurationSupplier configuration = new AgroalDataSourceConfigurationSupplier()
                .dataSourceImplementation(AgroalDataSourceConfiguration.DataSourceImplementation.AGROAL)
                .metricsEnabled(celkDataSourceConfig.getMetrics())
                .connectionPoolConfiguration(cp -> cp
                        .minSize(celkDataSourceConfig.getMinSize())
                        .maxSize(celkDataSourceConfig.getMaxSize())
                        .maxLifetime(Duration.ofSeconds(celkDataSourceConfig.getMaxLifetime()))
                        .initialSize(celkDataSourceConfig.getInitialSize())
                        .connectionValidator(defaultValidator())
                        .acquisitionTimeout(Duration.ofSeconds(celkDataSourceConfig.getAcquisitionTimeout()))
                        .leakTimeout(Duration.ofSeconds(celkDataSourceConfig.getLeakTimeout()))
                        .validationTimeout(Duration.ofSeconds(celkDataSourceConfig.getValidationTimeout()))
                        .idleValidationTimeout(Duration.ofSeconds(celkDataSourceConfig.getIdleValidationTimeout()))
                        .reapTimeout(Duration.ofSeconds(celkDataSourceConfig.getReapTimeout()))
                        .connectionFactoryConfiguration(cf -> cf
                                .jdbcUrl(celkConfig.getUrlLeitura()+"?ApplicationName=PDA")
                                .connectionProviderClassName(celkDataSourceConfig.getDriver())
                                .autoCommit(celkDataSourceConfig.getAutoCommit())
                                .jdbcTransactionIsolation(READ_COMMITTED)
                                .principal(new NamePrincipal(celkConfig.getUser()))
                                .credential(new SimplePassword(celkConfig.getPass()))
                        )
                );
        return configuration.get();
    }

    public CelkConfig getDbConfig(String tenantName) {
        if (tenantName == null || "".equals(tenantName)){
            customTenantResolver.resolveTenantId();
            tenantName = TenantContext.getCurrentTenant();
        }
        if (datasourcesConfigDynamoCache.containsKey(tenantName)) {
            return datasourcesConfigDynamoCache.get(tenantName);
        }
        CelkConfig celkConfig = null;

        try {
            celkConfig = dynamoDbConfigService.get(tenantName);
        } catch (Exception e) {
            throw new IllegalStateException("No configuration of datasource found for tenant: " + tenantName);
        }
        if (celkConfig == null || celkConfig.getTenantName() == null) {
            throw new IllegalStateException("Não foi encontrado configuração para o tenant: " + tenantName + ". Entre em contato com o suporte.");
        }
        if (celkConfig.getDbname() != null) {
            datasourcesConfigDynamoCache.put(tenantName, celkConfig);
        }
        if (celkConfig.getDbname() == null) {
            celkConfig = dynamoDbConfigService.get(tenantName);
        }
        return celkConfig;
    }

    public CelkDataSourceConfig getCelkDataSourceConfig() {
        return celkDataSourceConfig;
    }
}
