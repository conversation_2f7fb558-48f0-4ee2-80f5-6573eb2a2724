package br.com.celk.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "celk.datasource.limits")
public class CelkDataSourceLimits {

    private Integer listPatientsLimit;
    private Integer listCareUnitLimit;
    private Integer listProceduresLimit;
    private Integer listSchedulesLimit;
    private Integer listProfessionalsLimit;
    private Integer listAppointmentsLimit;
    private Integer listMedicamentosLimit;

    public Integer getListProfessionalsLimit() {
        return listProfessionalsLimit;
    }

    public void setListProfessionalsLimit(Integer listProfessionalsLimit) {
        this.listProfessionalsLimit = listProfessionalsLimit;
    }

    public Integer getListSchedulesLimit() {
        return listSchedulesLimit;
    }

    public void setListSchedulesLimit(Integer listSchedulesLimit) {
        this.listSchedulesLimit = listSchedulesLimit;
    }

    public Integer getListAppointmentsLimit() {
        return listAppointmentsLimit;
    }

    public void setListAppointmentsLimit(Integer listAppointmentsLimit) {
        this.listAppointmentsLimit = listAppointmentsLimit;
    }

    public Integer getListProceduresLimit() {
        return listProceduresLimit;
    }

    public void setListProceduresLimit(Integer listProceduresLimit) {
        this.listProceduresLimit = listProceduresLimit;
    }

    public Integer getListCareUnitLimit() {
        return listCareUnitLimit;
    }

    public void setListCareUnitLimit(Integer listCareUnitLimit) {
        this.listCareUnitLimit = listCareUnitLimit;
    }

    public Integer getListPatientsLimit() {
        return this.listPatientsLimit;
    }

    public void setListPatientsLimit(Integer listPatientsLimit) {
        this.listPatientsLimit = listPatientsLimit;
    }

    public Integer getListMedicamentosLimit() {
        return listMedicamentosLimit;
    }

    public void setListMedicamentosLimit(Integer listMedicamentosLimit) {
        this.listMedicamentosLimit = listMedicamentosLimit;
    }

}
