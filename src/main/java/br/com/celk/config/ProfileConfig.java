package br.com.celk.config;

import io.quarkus.runtime.configuration.ProfileManager;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import javax.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class ProfileConfig {
    private final String activeProfileQuarkus;

    @ConfigProperty(name = "quarkus.application.version")
    String version;

    public ProfileConfig() {
        this.activeProfileQuarkus = ProfileManager.getActiveProfile();
    }

    public boolean isQuarkusDevProfile() {
        return "dev".equalsIgnoreCase(activeProfileQuarkus);
    }

    public boolean isQuarkusHomProfile() {
        return "hom".equalsIgnoreCase(activeProfileQuarkus);
    }

    public boolean isQuarkusProdProfile() {
        return "prod".equalsIgnoreCase(activeProfileQuarkus);
    }

    public String getActiveProfileQuarkus() {
        return activeProfileQuarkus;
    }

    public String getVersion() {
        return version;
    }

}
