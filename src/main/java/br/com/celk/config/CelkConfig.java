package br.com.celk.config;

import br.com.celk.service.DynamoDbConfigAbstractService;
import io.quarkus.runtime.annotations.RegisterForReflection;
import org.jboss.logging.Logger;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.Map;
import java.util.Objects;

@RegisterForReflection
public class CelkConfig {
    private static final Logger LOG = Logger.getLogger(CelkConfig.class.getName());
    private String urlDomain;
    private String urlDatabase;
    private String urlDatabaseReading;
    private Integer port;
    private String user;
    private String pass;
    private String dbname;
    private String tenantName;
    private String UF;
    private String idCidade;

    public static CelkConfig from(Map<String, AttributeValue> item) {
        CelkConfig celkConfig = new CelkConfig();
        if (item != null && !item.isEmpty()) {
            celkConfig.setUrlDomain(item.get(DynamoDbConfigAbstractService.CELK_CONFIG_URL_DOMAIN).s());
            celkConfig.setTenantName(item.get(DynamoDbConfigAbstractService.CELK_CONFIG_TENANT_NAME).s());
            celkConfig.setDbname(item.get(DynamoDbConfigAbstractService.CELK_CONFIG_DBNAME).s());
            celkConfig.setUrlDatabase(item.get(DynamoDbConfigAbstractService.CELK_CONFIG_URL_DATABASSE).s());
            celkConfig.setUrlDatabaseReading(item.get(DynamoDbConfigAbstractService.CELK_CONFIG_URL_DATABASE_READING).s());
            celkConfig.setPort(Integer.valueOf(item.get(DynamoDbConfigAbstractService.CELK_CONFIG_PORT).s()));
            celkConfig.setUser(item.get(DynamoDbConfigAbstractService.CELK_CONFIG_USER).s());
            celkConfig.setPass(item.get(DynamoDbConfigAbstractService.CELK_CONFIG_PASS).s());
            AttributeValue valueUf = item.get(DynamoDbConfigAbstractService.CELK_CONFIG_UF);
            celkConfig.setUF(valueUf == null ? "" : valueUf.s());

            AttributeValue valueIdCidade = item.get(DynamoDbConfigAbstractService.CELK_CONFIG_ID_CIDADE);
            celkConfig.setIdCidade(valueIdCidade == null ? "" : valueIdCidade.s());

        }
        return celkConfig;
    }

    public String getUrlEnabledTracing() {
        return "jdbc:tracing:postgresql://" + this.urlDatabase + ":" + this.port + "/" + this.dbname;
    }

    public String getUrlEscrita() {
        String urlConnection = "jdbc:postgresql://"
                .concat(this.urlDatabase)
                .concat(":")
                .concat(String.valueOf(this.port))
                .concat("/")
                .concat(this.dbname);
        LOG.info("URL JDBC Connection [Escrita]- ".concat(urlConnection));
        return urlConnection.concat("?ApplicationName=PDA");
    }

    public String getUrlLeitura() {
        String urlConnection = "jdbc:postgresql://" + this.urlDatabaseReading + ":" + this.port + "/" + this.dbname;
        LOG.info("JDBC Connection - ".concat(urlConnection));
        return urlConnection;
    }

    public void setUrlDatabase(String urlDatabase) {
        this.urlDatabase = urlDatabase;
    }

    public Integer getPort() {
        return this.port == null ? 5432 : this.port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUser() {
        return this.user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPass() {
        return this.pass;
    }

    public void setPass(String pass) {
        this.pass = pass;
    }

    public String getDbname() {
        return this.dbname;
    }

    public void setDbname(String dbname) {
        this.dbname = dbname;
    }

    public String getUrlDatabase() {
        return this.urlDatabase;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getUrlDomain() {
        return urlDomain;
    }

    public void setUrlDomain(String urlDomain) {
        this.urlDomain = urlDomain;
    }

    public String getUrlDatabaseReading() {
        return urlDatabaseReading;
    }

    public void setUrlDatabaseReading(String urlDatabaseReading) {
        this.urlDatabaseReading = urlDatabaseReading;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getIdCidade() {
        return idCidade;
    }

    public void setIdCidade(String idCidade) {
        this.idCidade = idCidade;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CelkConfig that = (CelkConfig) o;
        return Objects.equals(urlDomain, that.urlDomain) &&
                Objects.equals(urlDatabase, that.urlDatabase) &&
                Objects.equals(urlDatabaseReading, that.urlDatabaseReading) &&
                Objects.equals(port, that.port) &&
                Objects.equals(user, that.user) &&
                Objects.equals(pass, that.pass) &&
                Objects.equals(dbname, that.dbname) &&
                Objects.equals(tenantName, that.tenantName) &&
                Objects.equals(UF, that.UF) &&
                Objects.equals(idCidade, that.idCidade);
    }

    @Override
    public int hashCode() {
        return Objects.hash(urlDomain, urlDatabase, urlDatabaseReading, port, user, pass, dbname, tenantName, UF, idCidade);
    }

    @Override
    public String toString() {
        return "CelkConfig{" +
                "urlDomain='" + urlDomain + '\'' +
                ", urlDatabase='" + urlDatabase + '\'' +
                ", urlDatabaseReading='" + urlDatabaseReading + '\'' +
                ", port=" + port +
                ", user='" + user + '\'' +
                ", pass='" + pass + '\'' +
                ", dbname='" + dbname + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", uf='" + UF + '\'' +
                ", idCidade='" + idCidade + '\'' +
                '}';
    }
}
