package br.com.celk.config.exception;

import javax.json.Json;
import javax.json.JsonObjectBuilder;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

class ExceptionUtils {

    private ExceptionUtils() {}

    static Response getResponse(String name, Status status, String message) {
        JsonObjectBuilder entityBuilder = Json.createObjectBuilder()
            .add("exceptionType", name)
            .add("code", status.getStatusCode());

        if (message != null) {
            entityBuilder.add("error", message);
        }

        return Response.status(status)
            .entity(entityBuilder.build())
            .build();
    }

}
