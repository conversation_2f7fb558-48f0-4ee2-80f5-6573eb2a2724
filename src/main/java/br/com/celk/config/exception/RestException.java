package br.com.celk.config.exception;

import javax.ws.rs.core.Response;
import java.io.Serializable;

public class RestException extends Exception implements Serializable {

    private static final long serialVersionUID = 1L;

    private Response.Status status;

    public RestException() {
        super();
    }

    public RestException(String msg) {
        super(msg);
    }

    public RestException(String msg, Exception e) {
        super(msg, e);
        this.status = Response.Status.BAD_REQUEST;
    }

    public RestException(Response.Status status, String mensagem) {
        super(mensagem);
        this.status = status;
    }

    public Response.Status getStatus() {
        return this.status;
    }
}