package br.com.celk.config.exception;

import org.jboss.logging.Logger;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;

import static br.com.celk.config.exception.ExceptionUtils.getResponse;

@Provider
public class RestExceptionMapper implements ExceptionMapper<RestException> {

    private static final Logger LOG = Logger.getLogger(RestExceptionMapper.class.getName());

    @Override
    public Response toResponse(RestException exception) {
        LOG.error("Failed to handle request", exception);

        return getResponse(exception.getClass().getName(), exception.getStatus(), exception.getMessage());
    }
}