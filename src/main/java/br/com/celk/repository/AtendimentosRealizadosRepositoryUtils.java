package br.com.celk.repository;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.web.dto.params.AtendimentosRealizadosParamDTO;

import javax.persistence.Query;
import java.time.YearMonth;

public class AtendimentosRealizadosRepositoryUtils {

    private AtendimentosRealizadosRepositoryUtils() {
    }

    public static String getAtendimentosRealizadosQuery() {
        String query =
                "select  cgp.cd_guia as idGuia," +
                        " case cgp.status " +
                        "    when 0 then 'ABERTA' " +
                        "    when 1 then 'A PAGAR' " +
                        "    when 2 then 'PAGA' " +
                        "    when 3 then 'CANCELADA' " +
                        "    else '-' " +
                        "  end as situacao, " +
                        " cgp.nr_chave as chaveRegulacao , "+
                        " cgp.nr_sisreg as sistemaRegulacao, "+
                        " cgp.cd_usu_cadsus as codCadSus , "+
                        " cgp.nm_paciente as nomePaciente, "+
                        " uc.cpf as cpfPaciente, "+
                        " cgp.cns_paciente as cnsPaciente, "+
                        " uc.cd_municipio_residencia as codigoIbgeOrigemPaciente, "+
                        " c.descricao as municipioOrigemPaciente, "+
                        " e.descricao as unidadeExecutante, "+
                        " e.cod_cid as codigoIbgeUnidadeExecutante, "+
                        " ce.descricao as municipioUnidadeExecutante, "+
                        " cp2.referencia as codigoProcedimentoSIGTAP, "+
                        " cp2.ds_procedimento as descricaoProcedimentoSIGTAP, "+
                        " p.referencia as codigoProcedimentoOriginal, "+
                        " p.ds_procedimento as descricaoProcedimentoOriginal, "+
                        " cgp.dt_agendamento as dataAgendamento , "+
                        " cgp.dt_aplicacao as dataAtendimento, "+
                        " cgp.dt_pagamento as dataFaturamento "+
                " from consorcio_guia_procedimento cgp "+
                " join consorcio_guia_proc_item cgpi on cgpi.cd_guia = cgp.cd_guia "+
                " join consorcio_procedimento cp2 on cgpi.cd_consorcio_procedimento = cp2.cd_consorcio_procedimento "+
                " left join procedimento p on p.cd_procedimento = cp2.cd_procedimento "+
                " join consorcio_prestador cp on cp.cd_prestador = cgp.cd_prestador "+
                " left join recibo_consorcio rc on rc.cd_recibo = cgp.cd_recibo "+
                " inner join usuario_cadsus uc on uc.cd_usu_cadsus = cgp.cd_usu_cadsus "+
                " inner join cidade c on uc.cd_municipio_residencia  = c.cod_cid "+
                " left outer join empresa e on cgp.empresa_consorcio = e.empresa "+
                " left outer join cidade ce on e.cod_cid = ce.cod_cid "+
                " where cgp.dt_aplicacao between :minDate and :maxDate " +
                " order by cgp.cd_guia "+
                " offset :offset " +
                " limit :limit ";

        return query;
    }

    public static void setDateParams(Query nativeQuery, YearMonth startDate) {
        nativeQuery.setParameter("minDate", startDate.atDay(1));
        nativeQuery.setParameter("maxDate", startDate.atEndOfMonth());
    }

    public static void setLimitOffsetParams(Query nativeQuery, AtendimentosRealizadosParamDTO param, CelkDataSourceLimits dataSourceLimits) {
        nativeQuery.setParameter("offset", (param.getPageNumber() - 1) * dataSourceLimits.getListAppointmentsLimit());
        nativeQuery.setParameter("limit", dataSourceLimits.getListAppointmentsLimit());
    }

}