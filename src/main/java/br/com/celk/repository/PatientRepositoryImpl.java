package br.com.celk.repository;

import br.com.celk.config.exception.RestException;
import br.com.celk.model.Patient;
import br.com.celk.model.TotalCount;
import br.com.celk.web.dto.params.PatientParamV1DTO;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.Query;
import java.util.List;

@Repository
public class PatientRepositoryImpl extends BaseRepository {

    private final PatientRepository patientRepository;

    public PatientRepositoryImpl(PatientRepository patientRepository) {
        this.patientRepository = patientRepository;
    }

    public List<Patient> listPatients(PatientParamV1DTO patientParamDTO) throws RestException {
        Query nativeQuery = this.entityManager.createNativeQuery(PatientRepositoryUtils.getListPatientsQuery(patientParamDTO), Patient.class);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        PatientRepositoryUtils.setListPatientsQueryParams(nativeQuery, patientParamDTO);
        PatientRepositoryUtils.setListPatientsQueryLimitOffsetParams(nativeQuery, patientParamDTO);
        //noinspection unchecked
        return nativeQuery.getResultList();
    }

    public Long countCareUnits(PatientParamV1DTO patientParamDTO) throws RestException {
        Query nativeQuery = this.entityManager.createNativeQuery(PatientRepositoryUtils.getCountPatientsQuery(patientParamDTO), TotalCount.class);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        PatientRepositoryUtils.setListPatientsQueryParams(nativeQuery, patientParamDTO);
        return ((TotalCount) nativeQuery.getSingleResult()).getTotal();
    }

    /**
     * Quando não existir uma conexão direta com banco, ex.: quando os endpoints se conectam com CS
     * o quarkus não executa a classe TenantResolver, dessa forma não é obtido o tenant da requisição.
     * Para resolver basta invocar esse método, onde ao executar EntityManager, o quarkus executa o resolver do tenant.
     */
    @Transactional
    public void startTenantResolver() {
        this.entityManager.clear();
    }
}
