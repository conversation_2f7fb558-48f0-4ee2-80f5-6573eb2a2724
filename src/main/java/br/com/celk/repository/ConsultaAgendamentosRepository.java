package br.com.celk.repository;

import br.com.celk.web.dto.ConsultaAgendamentoDTO;
import br.com.celk.web.dto.params.ConsultaAgendamentoParamDTO;
import org.jboss.logging.Logger;
import software.amazon.awssdk.utils.CollectionUtils;
import software.amazon.awssdk.utils.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Query;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
public class ConsultaAgendamentosRepository extends BaseRepository {

    private static final Logger LOG = Logger.getLogger(ConsultaAgendamentosRepository.class);

    public List<ConsultaAgendamentoDTO> getConsultaAgendamento(ConsultaAgendamentoParamDTO param) {
        LOG.info("Iniciando query ConsultaAgendamento.");
        validate(param);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        Query queryConsultaAgendamento = entityManager.createNativeQuery(ConsultaAgendamentosRepositoryUtils.getQueryConsultaAgendamentos(param), ConsultaAgendamentoDTO.class);
        ConsultaAgendamentosRepositoryUtils.setLimitOffsetParams(queryConsultaAgendamento, param, dataSourceLimits);

        List<ConsultaAgendamentoDTO> listConsultaAgendamentos = queryConsultaAgendamento.getResultList();
        if (CollectionUtils.isNullOrEmpty(listConsultaAgendamentos)) {
            return Collections.emptyList();
        }
        return listConsultaAgendamentos;
    }

    private void validate(ConsultaAgendamentoParamDTO param)  {
        if (param.getNumeroPagina() < 1) {
            param.setNumeroPagina(1);
        }
        if (param.getApenasAgendamentoNaoConfirmado() == null) {
            param.setApenasAgendamentoNaoConfirmado(false);
        }
        param.setAgendarConsultaViaPDA(isAgendarConsultaViaPDA());
    }

    private boolean isAgendarConsultaViaPDA() {
        Query queryRes = entityManager.createNativeQuery(ConsultaAgendamentosRepositoryUtils.getQueryIsAgendarConsultaViaPDA());
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        if(!StringUtils.isEmpty(queryRes.getSingleResult().toString()) &&
            StringUtils.isNotBlank(queryRes.getSingleResult().toString())){
            String retorno = queryRes.getSingleResult().toString();
            return !retorno.equals("N");
        }
        return false;
    }
}
