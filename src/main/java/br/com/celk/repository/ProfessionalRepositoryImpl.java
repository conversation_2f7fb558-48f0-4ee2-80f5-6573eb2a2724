package br.com.celk.repository;

import br.com.celk.model.Professional;
import br.com.celk.web.dto.params.PageParamDTO;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ProfessionalRepositoryImpl extends BaseRepository {

    private final ProfessionalRepository professionalRepository;

    public ProfessionalRepositoryImpl(ProfessionalRepository professionalRepository) {
        this.professionalRepository = professionalRepository;
    }

    public List<Professional> listProfessionals(PageParamDTO pageParamDTO) {
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        
        Pageable pageable = PageRequest.of(
            pageParamDTO.getQueryPageNumber(), 
            dataSourceLimits.getListProfessionalsLimit(), 
            Sort.by("professionalId").ascending()
        );
        return professionalRepository.findAll(pageable).getContent();
    }

    public long count() {
        return professionalRepository.count();
    }
}
