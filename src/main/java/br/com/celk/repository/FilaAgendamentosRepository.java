package br.com.celk.repository;

import br.com.celk.config.exception.RestException;
import br.com.celk.model.FilaAgendamento;
import br.com.celk.model.Procedimento;
import br.com.celk.web.dto.FilaAgendamentoDTO;
import br.com.celk.web.dto.ProcedimentoDTO;
import br.com.celk.web.dto.params.FilaAgendamentoParamDTO;
import br.com.celk.web.mapper.FilaAgendamentoMapperImpl;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;
import software.amazon.awssdk.utils.CollectionUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Query;
import javax.ws.rs.core.Response;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

@ApplicationScoped
public class FilaAgendamentosRepository extends BaseRepository {

    private static final Logger LOG = Logger.getLogger(FilaAgendamentosRepository.class);

    public List<FilaAgendamentoDTO> getFilaAgendamento(FilaAgendamentoParamDTO param) throws RestException {
        LOG.info("Iniciando query FilaAgendamento.");
        validate(param);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        Query queryFilaAgendamento = entityManager.createNativeQuery(FilaAgendamentosRepositoryUtils.getFilaAgendamentoQuery(), FilaAgendamento.class);
        FilaAgendamentosRepositoryUtils.setDateParams(queryFilaAgendamento, Date.valueOf(param.getStartDate()));
        FilaAgendamentosRepositoryUtils.setLimitOffsetParams(queryFilaAgendamento, param, dataSourceLimits);

        List<FilaAgendamento> listFilaAgendamentos = queryFilaAgendamento.getResultList();
        if (CollectionUtils.isNullOrEmpty(listFilaAgendamentos)) {
            return Collections.emptyList();
        }

        List<FilaAgendamentoDTO> listFilaAgendamentosDTO = new FilaAgendamentoMapperImpl().toDto(listFilaAgendamentos);
        setListProcedimentos(listFilaAgendamentosDTO);

        return listFilaAgendamentosDTO;
    }

    private void validate(FilaAgendamentoParamDTO param) throws RestException {
        if (StringUtils.isEmpty(param.getStartDate())) {
            throw new RestException(Response.Status.BAD_REQUEST, "Informe uma data");
        } else {
            try {
                LocalDate data = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern("dd-MM-yyyy"));
                param.setStartDate(data.toString());
            } catch (DateTimeParseException e) {
                throw new RestException(Response.Status.BAD_REQUEST, "Start date invalid (Valid format: dd-MM-yyyy)");
            }
        }
    }

    private void setListProcedimentos(List<FilaAgendamentoDTO> listFilaAgendamentosDTO) {
        List<Long> codigosSolicitacoes = listFilaAgendamentosDTO.stream().map(FilaAgendamentoDTO::getIdSolicitacao).collect(Collectors.toList());
        Query queryProcedimento = entityManager.createNativeQuery(FilaAgendamentosRepositoryUtils.getQueryProcedimentos(), Procedimento.class);
        FilaAgendamentosRepositoryUtils.setCodSolicitacoes(queryProcedimento, codigosSolicitacoes);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        List<Procedimento> listProcedimentos = queryProcedimento.getResultList();
        Map<Long, List<Procedimento>> mapSolicitacaoProcedimentos = listProcedimentos.stream().collect(Collectors.groupingBy(Procedimento::getIdSolicitacao, HashMap::new, Collectors.toCollection(ArrayList::new)));

        for (FilaAgendamentoDTO fa : listFilaAgendamentosDTO) {
            List<ProcedimentoDTO> listProcedimentoDTO = new ArrayList<>();
            List<Procedimento> procedimentosByAgendamento = mapSolicitacaoProcedimentos.get(fa.getIdSolicitacao());

            if (procedimentosByAgendamento != null) {
                procedimentosByAgendamento.forEach(p -> {
                    listProcedimentoDTO.add(new ProcedimentoDTO(p.getProcedimento(),
                            p.getDataAgendamento(),
                            p.getDataConfirmacao(),
                            p.getDataAtendimento(),
                            p.getClassificacaoRisco(),
                            p.getUnidadeDesejada(),
                            p.getComplexidade(),
                            p.getCategoriaProcedimento()
                    ));
                });
                fa.setItensProcedimentos(listProcedimentoDTO);
            }
        }
    }
}
