package br.com.celk.repository;

import br.com.celk.model.Procedure;
import br.com.celk.web.dto.params.PageParamDTO;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ProcedureRepositoryImpl extends BaseRepository {

    private final ProcedureRepository procedureRepository;

    public ProcedureRepositoryImpl(ProcedureRepository procedureRepository) {
        this.procedureRepository = procedureRepository;
    }

    public List<Procedure> listProcedures(PageParamDTO pageParamDTO) {
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        
        Pageable pageable = PageRequest.of(
            pageParamDTO.getQueryPageNumber(), 
            dataSourceLimits.getListProceduresLimit(), 
            Sort.by("procedureId").ascending()
        );
        return procedureRepository.findAll(pageable).getContent();
    }

    public long count() {
        return procedureRepository.count();
    }
}
