package br.com.celk.repository;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.web.dto.params.FilaGuiasPagasPrestadorParamDTO;

import javax.persistence.Query;
import java.time.YearMonth;

public class FilaGuiasPagasPrestadorRepositoryUtils {

    private FilaGuiasPagasPrestadorRepositoryUtils() {
    }

    public static String getFilaGuiasPagasPrestadorQuery() {

        return " select  cgp.cd_prestador as numeroPrestador, " +
                " e.descricao as nomePrestador, "+

                " sum(coalesce(cgpi.qtdade_aplicacao, 0)) as quantidadeRealizada, "+
                " sum(coalesce(cgpi.vl_procedimento , 0)) as valorTotal, "+

                " string_agg(distinct to_char(cgp.dt_aplicacao, 'yyyy-mm-dd'), ', ') as competencia, "+
                " string_agg(distinct cp2.referencia, ', ') as codigoProcedimentoSIGTAP, "+
                " string_agg(distinct cp2.ds_procedimento, ', ') as descricaoProcedimentoSIGTAP, "+
                " string_agg(distinct p.referencia, ', ') as codigoProcedimentoOriginal, "+
                " string_agg(distinct p.ds_procedimento, ', ') as descricaoProcedimentoOriginal "+

                " from consorcio_guia_proc_item cgpi " +
                " left outer join consorcio_guia_procedimento cgp on cgpi.cd_guia=cgp.cd_guia " +
                " left outer join consorcio_procedimento cp2 on cgpi.cd_consorcio_procedimento = cp2.cd_consorcio_procedimento " +
                " left outer join procedimento p on cp2.cd_procedimento = p.cd_procedimento " +
                " left outer join consorcio_prestador consorciop3_ on cgp.cd_prestador=consorciop3_.cd_prestador " +
                " left outer join empresa e on consorciop3_.empresa_prestador=e.empresa "+

                " where cgp.dt_aplicacao between :minDate and :maxDate " +
                " and cgp.status = 2 " +/*Status 2 são guias Pagas*/

                " group by cgp.cd_prestador, e.descricao "+
                " order by e.descricao asc" +
                " offset :offset " +
                " limit :limit ";
    }

    public static void setDateParams(Query nativeQuery, YearMonth startDate) {
        nativeQuery.setParameter("minDate", startDate.atDay(1));
        nativeQuery.setParameter("maxDate", startDate.atEndOfMonth());
    }

    public static void setLimitOffsetParams(Query nativeQuery, FilaGuiasPagasPrestadorParamDTO param, CelkDataSourceLimits dataSourceLimits) {
        nativeQuery.setParameter("offset", (param.getPageNumber() - 1) * dataSourceLimits.getListAppointmentsLimit());
        nativeQuery.setParameter("limit", dataSourceLimits.getListAppointmentsLimit());
    }

}