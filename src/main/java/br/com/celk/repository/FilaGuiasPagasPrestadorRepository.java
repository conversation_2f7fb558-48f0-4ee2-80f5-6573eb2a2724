package br.com.celk.repository;

import br.com.celk.config.exception.RestException;
import br.com.celk.model.FilaGuiasPagasPrestador;
import br.com.celk.web.dto.FilaGuiasPagasPrestadorDTO;
import br.com.celk.web.dto.params.FilaGuiasPagasPrestadorParamDTO;
import br.com.celk.web.mapper.FilaGuiasPagasPrestadorMapperImpl;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;
import software.amazon.awssdk.utils.CollectionUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Query;
import javax.ws.rs.core.Response;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
public class FilaGuiasPagasPrestadorRepository extends BaseRepository {

    private static final Logger LOG = Logger.getLogger(FilaGuiasPagasPrestadorRepository.class);

    public List<FilaGuiasPagasPrestadorDTO> getFilaGuiasPagasPrestador(FilaGuiasPagasPrestadorParamDTO param) throws RestException {
        LOG.info("Iniciando query FilaGuiasPagasPrestador.");
        validate(param);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        Query queryFilaGuiasPagasPrestador = entityManager.createNativeQuery(FilaGuiasPagasPrestadorRepositoryUtils.getFilaGuiasPagasPrestadorQuery(), FilaGuiasPagasPrestador.class);
        FilaGuiasPagasPrestadorRepositoryUtils.setDateParams(queryFilaGuiasPagasPrestador, YearMonth.parse(param.getStartDate()));
        FilaGuiasPagasPrestadorRepositoryUtils.setLimitOffsetParams(queryFilaGuiasPagasPrestador, param, dataSourceLimits);

        List<FilaGuiasPagasPrestador> listFilaAgendamentos = queryFilaGuiasPagasPrestador.getResultList();
        if (CollectionUtils.isNullOrEmpty(listFilaAgendamentos)) {
            return Collections.emptyList();
        }

        return new FilaGuiasPagasPrestadorMapperImpl().toDto(listFilaAgendamentos);
    }

    private void validate(FilaGuiasPagasPrestadorParamDTO param) throws RestException {
        if (StringUtils.isEmpty(param.getStartDate())) {
            throw new RestException(Response.Status.BAD_REQUEST, "Informe uma data");
        } else {
            try {
                YearMonth data = YearMonth.parse(param.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM"));
                param.setStartDate(data.toString());
            } catch (DateTimeParseException e) {
                throw new RestException(Response.Status.BAD_REQUEST, "Start date invalid (Valid format: yyyy-MM)");
            }
        }
    }


}
