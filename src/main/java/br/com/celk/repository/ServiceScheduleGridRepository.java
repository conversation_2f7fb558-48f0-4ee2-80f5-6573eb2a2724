package br.com.celk.repository;

import br.com.celk.model.GridScheduleService;
import br.com.celk.model.TotalCount;
import br.com.celk.web.dto.GridScheduleServiceDTO;
import br.com.celk.web.dto.params.ScheduleParamDTO;
import br.com.celk.web.mapper.GridScheduleServiceMapperImpl;
import io.quarkus.hibernate.orm.panache.PanacheRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.Query;
import java.util.List;

@ApplicationScoped
public class ServiceScheduleGridRepository extends BaseRepository implements PanacheRepository<GridScheduleService> {

    @Inject
    ScheduleTimeGridRepository scheduleTimeGridRepository;

    public List<GridScheduleServiceDTO> listSchedules(ScheduleParamDTO scheduleParamDTO) {
        Query nativeQuery = this.entityManager.createNativeQuery(ServiceScheduleGridRepositoryUtils.getListSchedulesQuery(scheduleParamDTO), GridScheduleService.class);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        ServiceScheduleGridRepositoryUtils.setListSchedulesQueryCommonParams(nativeQuery, scheduleParamDTO);
        ServiceScheduleGridRepositoryUtils.setListSchedulesQueryLimitOffsetParams(nativeQuery, scheduleParamDTO, dataSourceLimits);

        //noinspection unchecked
        List<GridScheduleServiceDTO> gridScheduleServices = new GridScheduleServiceMapperImpl().toDto(nativeQuery.getResultList());
        gridScheduleServices.forEach(gridScheduleService -> gridScheduleService.setTimeSchedules(this.scheduleTimeGridRepository.listSchedulesTimes(gridScheduleService)));

        return gridScheduleServices;

    }


    public Long countSchedules(ScheduleParamDTO scheduleParamDTO) {
        Query nativeQuery = this.entityManager.createNativeQuery(ServiceScheduleGridRepositoryUtils.getCountSchedulesQuery(scheduleParamDTO), TotalCount.class);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        ServiceScheduleGridRepositoryUtils.setListSchedulesQueryCommonParams(nativeQuery, scheduleParamDTO);
        return ((TotalCount) nativeQuery.getSingleResult()).getTotal() ;
    }
}
