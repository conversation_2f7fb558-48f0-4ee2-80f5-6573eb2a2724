package br.com.celk.repository;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.model.enuns.StatusAgendaGradeAtendimentoHorario;
import br.com.celk.model.enuns.StatusSolicitacaoAgendamento;
import br.com.celk.model.enuns.StatusTipoAtendimento;
import br.com.celk.web.dto.params.ConsultaAgendamentoParamDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.Query;

public class ConsultaAgendamentosRepositoryUtils {

    private static final Logger log = LoggerFactory.getLogger(ConsultaAgendamentosRepositoryUtils.class);

    private ConsultaAgendamentosRepositoryUtils(ConsultaAgendamentoParamDTO param) {
    }

    public static String getQueryConsultaAgendamentos(ConsultaAgendamentoParamDTO param) {
        String query = "select " +
                " ag.cd_ag_gra_ate_hor as idAgendamento, " +
                " sa.cd_solicitacao as idSolicitacao," +
                " sa.cd_usu_cadsus as idPaciente," +
                " uc.nm_usuario as nmPaciente," +
                " uc.dt_nascimento as dtNascimento," +
                " uc.nr_telefone as nrTelefone1," +
                " uc.nr_telefone_2 as nrTelefone2," +
                " uc.telefone3 as nrTelefone3," +
                " uc.telefone4 as nrTelefone4," +
                " uc.celular as nrCelular," +
                " sa.dt_solicitacao as dtSolicitacao," +
                " sa.dt_processamento_lote as dtQueFoiAgendado," +
                " sa.dt_agendamento as dtAgendamento," +
                " eor.descricao as estabelecimentoInsercao," +
                " eex.descricao as estabelecimentoExecucao," +
                " uc.email as emailPaciente," +
                " tp.cd_tp_procedimento as idTpProcedimento," +
                " tp.ds_tp_procedimento as dsTpProcedimento," +
                " string_agg(ep.ds_procedimento, ',') as procedimento " +
                " from " +
                " agenda_gra_ate_horario ag " +
                " join agenda_grade_atendimento aga on aga.cd_ag_gra_atendimento = ag.cd_ag_gra_atendimento "+
                " join solicitacao_agendamento sa on ag.cd_solicitacao = sa.cd_solicitacao " +
                " left join solicitacao_agendamento_exame sae on sae.cd_solicitacao = sa.cd_solicitacao " +
                " left join exame_procedimento ep on ep.cd_exame_procedimento = sae.cd_exame_procedimento " +
                " join tipo_procedimento tp on tp.cd_tp_procedimento = sa.cd_tp_procedimento " +
                " join usuario_cadsus uc on uc.cd_usu_cadsus = ag.cd_usu_cadsus " +
                " join empresa eor on eor.empresa = sa.unidade " +
                " join empresa eex on eex.empresa = sa.unidade_executante " +
                " where ag.cd_ag_hr_principal is null "+
                " and ag.status not in ("+StatusAgendaGradeAtendimentoHorario.CANCELADO.getValue()+
                                    ","+StatusAgendaGradeAtendimentoHorario.REMANEJADO.getValue()+")";

        if (param.getApenasAgendamentoNaoConfirmado()) {
            query += " and sa.dt_conf_usuario is null ";
        }
        if (param.getAgendarConsultaViaPDA()){
            query += " and aga.cd_tipo in ("+ StatusTipoAtendimento.TIPO_CONSULTA.getValue()+
                                            ","+StatusTipoAtendimento.APP_CIDADAO.getValue()+") ";
        }
        if (param.getIdTipoProcedimento() != null) {
            query += " and tp.cd_tp_procedimento = " + param.getIdTipoProcedimento();
        }
        query += " and sa.status <> " + StatusSolicitacaoAgendamento.STATUS_CANCELADO.getValue() +
                " and ag.dt_agendamento >= current_date " +
                " group by 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18 " +
                " order by ag.cd_ag_gra_ate_hor " +
                " limit :limit offset :offset";

        return query;
    }

    public static void setLimitOffsetParams(Query nativeQuery, ConsultaAgendamentoParamDTO param, CelkDataSourceLimits dataSourceLimits) {
        nativeQuery.setParameter("offset", (param.getNumeroPagina() - 1) * dataSourceLimits.getListAppointmentsLimit());
        nativeQuery.setParameter("limit", dataSourceLimits.getListAppointmentsLimit());
    }


    public static String getQueryIsAgendarConsultaViaPDA(){
        return  "select valor " +
                " from parametro_gem pg " +
                " where cd_modulo = 30" +
                " and pg.parametro = 'AgendarConsultaViaPDA' ";
    }

}