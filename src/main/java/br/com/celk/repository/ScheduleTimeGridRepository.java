package br.com.celk.repository;

import br.com.celk.model.ScheduleTimeGrid;
import br.com.celk.model.enuns.ScheduleTimeGridStatus;
import br.com.celk.web.dto.GridScheduleServiceDTO;
import br.com.celk.web.dto.ScheduleTimeGridDTO;
import br.com.celk.web.mapper.ScheduleTimeGridMapperImpl;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.quarkus.panache.common.Parameters;
import io.quarkus.panache.common.Sort;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class ScheduleTimeGridRepository extends BaseRepository implements PanacheRepository<ScheduleTimeGrid> {

    public List<ScheduleTimeGridDTO> listSchedulesTimes(GridScheduleServiceDTO gridScheduleService) {
        String query = "serviceScheduleGridId = :serviceScheduleGridId and status = :status";
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        Sort sort = Sort.by("scheduleTimeGridId");
        Parameters params = new Parameters()
                .and("serviceScheduleGridId", gridScheduleService.getGridScheduleServiceId())
                .and("status", (long) ScheduleTimeGridStatus.PENDENTE.getValue());

        return new ScheduleTimeGridMapperImpl().toDto(find(query, sort, params).list());
    }
}
