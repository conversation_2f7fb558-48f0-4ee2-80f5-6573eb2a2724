package br.com.celk.repository;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.config.tenant.RequestContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@Repository
public abstract class BaseRepository {

    @PersistenceContext
    protected EntityManager entityManager;

    @Autowired
    protected CelkDataSourceLimits dataSourceLimits;

    @Autowired
    protected RequestContext requestContext;

    /**
     * Quando não existir uma conexão direta com banco, ex.: quando os endpoints se conectam com CS
     * o quarkus não executa a classe TenantResolver, dessa forma não é obtido o tenant da requisição.
     * Para resolver basta invocar esse método, onde ao executar EntityManager, o quarkus executa o resolver do tenant.
     */
    @Transactional
    public void startTenantResolver() {
        this.entityManager.clear();
    }

    public EntityManager getEntityManager() {
        return entityManager;
    }

    public void setEntityManager(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    public CelkDataSourceLimits getDataSourceLimits() {
        return dataSourceLimits;
    }

    public void setDataSourceLimits(CelkDataSourceLimits dataSourceLimits) {
        this.dataSourceLimits = dataSourceLimits;
    }

    public RequestContext getRequestContext() {
        return requestContext;
    }

    public void setRequestContext(RequestContext requestContext) {
        this.requestContext = requestContext;
    }
}
