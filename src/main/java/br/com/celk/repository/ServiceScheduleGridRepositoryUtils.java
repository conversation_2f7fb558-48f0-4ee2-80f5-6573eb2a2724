package br.com.celk.repository;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.model.GridScheduleService;
import br.com.celk.model.enuns.AppointmentType;
import br.com.celk.model.enuns.ScheduleTimeGridStatus;
import br.com.celk.web.dto.params.ScheduleParamDTO;
import io.quarkus.hibernate.orm.panache.PanacheRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Query;
import java.time.OffsetDateTime;
import java.util.Arrays;

@ApplicationScoped
public class ServiceScheduleGridRepositoryUtils implements PanacheRepository<GridScheduleService> {

    private ServiceScheduleGridRepositoryUtils() {
    }

    public static String getListSchedulesQuery(ScheduleParamDTO scheduleParamDTO) {
        return "select " +
                "    agenda_grade_atendimento.cd_ag_gra_atendimento, " +
                "    agenda_grade_atendimento.tempo_medio, " +
                "    agenda_grade.cd_ag_grade, " +
                "    agenda.cd_agenda, " +
                "    agenda.empresa, " +
                "    agenda.cd_profissional, " +
                "    agenda.cd_tp_procedimento, " +
                "    tipo_atendimento_agenda.cd_tipo, " +
                "    tipo_atendimento_agenda.ds_tipo, " +
                "    tipo_atendimento_agenda.tp_atendimento " +
                getFrom() +
                getWhere(scheduleParamDTO) +
                getOrderBy() +
                "offset :offset " +
                "limit :limit";
    }

    public static String getCountSchedulesQuery(ScheduleParamDTO scheduleParamDTO) {
        return "select " +
                "    count(agenda_grade_atendimento.cd_ag_gra_atendimento) as total " +
                getFrom() +
                getWhere(scheduleParamDTO);
    }

    private static String getFrom() {
        return "from " +
                "    agenda_grade_atendimento " +
                "join tipo_atendimento_agenda on tipo_atendimento_agenda.cd_tipo = agenda_grade_atendimento.cd_tipo " +
                "join agenda_grade on agenda_grade.cd_ag_grade = agenda_grade_atendimento.cd_ag_grade " +
                "join agenda on agenda.cd_agenda = agenda_grade.cd_agenda " +
                "join profissional on profissional.cd_profissional = agenda.cd_profissional ";
    }

    private static String getWhere(ScheduleParamDTO scheduleParamDTO) {
        return "where " +
                "exists (select 1 from agenda_grade_horario where agenda_grade_horario.cd_ag_gra_atendimento = agenda_grade_atendimento.cd_ag_gra_atendimento and agenda_grade_horario.status = :status) " +
                "and agenda_grade.data between :minDate and :maxDate " +
                "and tipo_atendimento_agenda.tp_atendimento in (:appointmentTypeIds) " +
                (scheduleParamDTO.getCareUnitId() != null ? "and agenda.empresa = :careUnitId " : "") +
                (scheduleParamDTO.getProfessionalId() != null ? "and agenda.cd_profissional = :professionalId " : "") +
                (scheduleParamDTO.getProcedureId() != null ? "and agenda.cd_tp_procedimento = :procedureId " : "") +
                (scheduleParamDTO.getProfessionalSpecialtyId() != null ? "and exists (select 1 from profissional_carga_horaria where profissional_carga_horaria.cd_profissional = profissional.cd_profissional and profissional_carga_horaria.cd_cbo = :professionalSpecialtyId) " : "");
    }

    private static String getOrderBy() {
        return "order by  " +
                "    agenda_grade_atendimento.cd_ag_gra_atendimento ";
    }

    public static void setListSchedulesQueryLimitOffsetParams(Query nativeQuery, ScheduleParamDTO scheduleParamDTO, CelkDataSourceLimits dataSourceLimits) {
        nativeQuery.setParameter("offset", (scheduleParamDTO.getPageNumber() - 1) * dataSourceLimits.getListSchedulesLimit());
        nativeQuery.setParameter("limit", dataSourceLimits.getListSchedulesLimit());
    }

    public static void setListSchedulesQueryCommonParams(Query nativeQuery, ScheduleParamDTO scheduleParamDTO) {
        nativeQuery.setParameter("status", ScheduleTimeGridStatus.PENDENTE.getValue());
        nativeQuery.setParameter("minDate", OffsetDateTime.now().toLocalDate());
        nativeQuery.setParameter("maxDate", OffsetDateTime.now().toLocalDate().plusDays(scheduleParamDTO.getAmountOfDays() - 1));
        if (scheduleParamDTO.getProfessionalId() != null) {
            nativeQuery.setParameter("professionalId", scheduleParamDTO.getProfessionalId());
        }
        if (scheduleParamDTO.getCareUnitId() != null) {
            nativeQuery.setParameter("careUnitId", scheduleParamDTO.getCareUnitId());
        }
        if (scheduleParamDTO.getProfessionalSpecialtyId() != null) {
            nativeQuery.setParameter("professionalSpecialtyId", scheduleParamDTO.getProfessionalSpecialtyId());
        }
        if (scheduleParamDTO.getProcedureId() != null) {
            nativeQuery.setParameter("procedureId", scheduleParamDTO.getProcedureId());
        }
        if (isAppointmentTypesNotEmpty(scheduleParamDTO)) {
            nativeQuery.setParameter("appointmentTypeIds", scheduleParamDTO.getAppointmentTypeIds());
        } else {
            nativeQuery.setParameter("appointmentTypeIds", Arrays.asList(AppointmentType.TELEAGENDAMENTO_NORMAL.getValue(), AppointmentType.TELEAGENDAMENTO_RETORNO.getValue()));
        }
    }

    private static boolean isAppointmentTypesNotEmpty(ScheduleParamDTO scheduleParamDTO) {
        return scheduleParamDTO.getAppointmentTypeIds() != null && !scheduleParamDTO.getAppointmentTypeIds().isEmpty();
    }
}
