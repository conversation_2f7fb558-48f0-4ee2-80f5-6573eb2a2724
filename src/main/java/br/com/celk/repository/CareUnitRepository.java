package br.com.celk.repository;

import br.com.celk.model.CareUnit;
import br.com.celk.web.dto.params.PageParamDTO;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CareUnitRepository extends JpaRepository<CareUnit, Long> {

    default List<CareUnit> listCareUnits(PageParamDTO pageParamDTO, int limit) {
        Pageable pageable = PageRequest.of(
            pageParamDTO.getQueryPageNumber(),
            limit,
            Sort.by("careUnitId").ascending()
        );
        return findAll(pageable).getContent();
    }
}
