package br.com.celk.repository;

import br.com.celk.config.exception.RestException;
import br.com.celk.model.AtendimentosRealizados;
import br.com.celk.web.dto.AtendimentosRealizadosDTO;
import br.com.celk.web.dto.params.AtendimentosRealizadosParamDTO;
import br.com.celk.web.mapper.AtendimentosRealizadosMapperImpl;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;
import software.amazon.awssdk.utils.CollectionUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Query;
import javax.ws.rs.core.Response;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
public class AtendimentosRealizadosRepository extends BaseRepository {

    private static final Logger LOG = Logger.getLogger(AtendimentosRealizadosRepository.class);

    public List<AtendimentosRealizadosDTO> getFilaAtendimentosRealizados(AtendimentosRealizadosParamDTO param) throws RestException {
        LOG.info("Iniciando query FilaAtendimentosRealizados");
        validate(param);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        Query queryAtendimentosRealizados = entityManager.createNativeQuery(AtendimentosRealizadosRepositoryUtils.getAtendimentosRealizadosQuery(), AtendimentosRealizados.class);
        AtendimentosRealizadosRepositoryUtils.setDateParams(queryAtendimentosRealizados, YearMonth.parse(param.getStartDate()));
        AtendimentosRealizadosRepositoryUtils.setLimitOffsetParams(queryAtendimentosRealizados, param, dataSourceLimits);

        List<AtendimentosRealizados> listFilaAtendimentosRealizados = queryAtendimentosRealizados.getResultList();
        if (CollectionUtils.isNullOrEmpty(listFilaAtendimentosRealizados)) {
            return Collections.emptyList();
        }

        List<AtendimentosRealizadosDTO> listAtendimentosRealizadosDTO = new AtendimentosRealizadosMapperImpl().toDto(listFilaAtendimentosRealizados);

        return trataCamposRetorno(listAtendimentosRealizadosDTO);
    }

    private void validate(AtendimentosRealizadosParamDTO param) throws RestException {
        if (StringUtils.isEmpty(param.getStartDate())) {
            throw new RestException(Response.Status.BAD_REQUEST, "Informe uma data");
        } else {
            try {
                YearMonth data = YearMonth.parse(param.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM"));
                param.setStartDate(data.toString());
            } catch (DateTimeParseException e) {
                throw new RestException(Response.Status.BAD_REQUEST, "Start date invalid (Valid format: yyyy-MM)");
            }
        }
    }

    private List<AtendimentosRealizadosDTO>  trataCamposRetorno( List<AtendimentosRealizadosDTO> lstAtendimentosRealizados) {
        for (AtendimentosRealizadosDTO realizadosDTO : lstAtendimentosRealizados) {
            if (realizadosDTO.getCpfPaciente() == null) {
                realizadosDTO.setCpfPaciente(" ");
            }

            if (realizadosDTO.getCnsPaciente() == null) {
                realizadosDTO.setCnsPaciente(" ");
            }

            if (realizadosDTO.getDataAtendimento() == null) {
                realizadosDTO.setDataAtendimento(" ");
            }

            if(realizadosDTO.getDataFaturamento() == null){
                realizadosDTO.setDataFaturamento(" ");
            }

            if ((realizadosDTO.getChaveRegulacao()== null && realizadosDTO.getSistemaRegulacao() == null)) {
                realizadosDTO.setSistemaRegulacao(" ");
            } else {
                realizadosDTO.setSistemaRegulacao("SISREG");
            }
            realizadosDTO.setProcedimentoComplementar(" ");
            realizadosDTO.setJustificativa("");
        }
        return lstAtendimentosRealizados;
    }

}
