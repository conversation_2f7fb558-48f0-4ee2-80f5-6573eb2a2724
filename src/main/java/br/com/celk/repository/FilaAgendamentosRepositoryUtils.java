package br.com.celk.repository;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.web.dto.params.FilaAgendamentoParamDTO;
import software.amazon.awssdk.utils.CollectionUtils;

import javax.persistence.Query;
import java.sql.Date;
import java.util.List;

public class FilaAgendamentosRepositoryUtils {

    private FilaAgendamentosRepositoryUtils() {
    }

    public static String getFilaAgendamentoQuery() {
        String query =
                " select sa.cd_solicitacao as idSolicitacao " +
                        ", tp.ds_tp_procedimento as tipoProcedimento " +
                        ", case sa.tp_consulta " +
                        "    when 0 THEN 'Consulta' " +
                        "    when 1 THEN 'Retorno' " +
                        "    else '-' " +
                        "  end as tipoConsulta " +
                        ", case sa.status " +
                        "    when 0 then 'Ag. Regulação' " +
                        "    when 1 then 'Pendente' " +
                        "    when 2 then 'Agendado' " +
                        "    when 3 then 'Negado <PERSON>ulação' " +
                        "    when 4 then 'Devolvido Reg<PERSON>ção' " +
                        "    when 5 then 'Agendado' " +
                        "    when 6 then 'Cancelado' " +
                        "    when 7 then 'Agendado' " +
                        "    when 8 then 'Ag. Analise' " +
                        "    when 9 then 'Devolvido' " +
                        "    when 10 then 'Ag. Autorização' " +
                        "    when 11 then 'Pendente' " +
                        "    when 12 then 'Bloqueado' " +
                        "    when 13 then 'Aprovado Regulação' " +
                        "    else '-' " +
                        "  end as situacao" +
                        ", case " +
                        "    when (sa.dt_agendamento is not null or sa.status = 6) then 0 " +
                        "    else coalesce(sapf.posicao_fila_espera, 9999999) " +
                        "  end as posicao " +
                        ", uc.cd_usu_cadsus as codPaciente " +
                        ", ucc.cd_numero_cartao as cns " +
                        ", uc.nm_usuario as paciente " +
                        ", uc.sg_sexo as sexo " +
                        ", to_char(uc.dt_nascimento, 'dd/MM/yyyy') as dataNasc " +
                        ", uc.nm_mae as nomeMae " +
                        ", c.descricao as cidade " +
                        ", case sa.prioridade  " +
                        "    when '1' then 'Brevidade' " +
                        "    when '2' then 'Eletivo' " +
                        "    else 'Urgente' " +
                        "  end as prioridade " +
                        ", sa.obs_urgente as obsPriorizacao " +
                        ", to_char(sa.dt_solicitacao, 'dd/MM/yyyy') as dataSolicitacaoAgendamento " +
                        " from solicitacao_agendamento sa " +
                        " left join tipo_procedimento tp on tp.cd_tp_procedimento = sa.cd_tp_procedimento " +
                        " left join usuario_cadsus uc on  uc.cd_usu_cadsus = sa.cd_usu_cadsus " +
                        " left join usuario_cadsus_cns ucc on ucc.cd_usu_cadsus = uc.cd_usu_cadsus and ucc.st_excluido = 0 " +
                        " left join solicitacao_agendamento_posicao_fila sapf on sapf.cd_solicitacao = sa.cd_solicitacao " +
                        " left join endereco_usuario_cadsus euc on uc.cd_endereco = euc.cd_endereco and euc.st_excluido = 0 " +
                        " left join cidade c on euc.cod_cid = c.cod_cid " +
                        " where coalesce(date(sa.dt_alteracao), current_date) between :startDate and current_date " +
                        " and tp.tfd = 'N' " +
                        " order by sa.cd_solicitacao " +
                        " offset :offset " +
                        " limit :limit ";

        return query;
    }

    public static String getQueryProcedimentos() {
        String query =
                " select row_number() OVER() AS id " +
                        " , sa.cd_solicitacao as idSolicitacao " +
                        " , sa.cd_usu_cadsus as idUsuarioCadsus " +
                        " , coalesce(p.cd_procedimento, p2.cd_procedimento) as idProcedimento " +
                        " , coalesce(p.ds_procedimento, p2.ds_procedimento) as procedimento " +
                        " , to_char(agah.dt_agendamento, 'dd/MM/yyyy') as dataAgendamento " +
                        " , to_char(agah.dt_confirmacao, 'dd/MM/yyyy') as dataConfirmacao " +
                        " , to_char(ate.dt_chegada, 'dd/MM/yyyy') as dataAtendimento " +
                        " , cr.descricao AS classificacaoRisco " +
                        " , e2.descricao AS unidadeDesejada " +
                        " , (CASE coalesce(pc.tp_complexidade, pc2.tp_complexidade)" +
                        "       WHEN 0 THEN 'Baixo' " +
                        "       WHEN 1 THEN 'Médio 1' " +
                        "       WHEN 2 THEN 'Médio 2' " +
                        "       WHEN 3 THEN 'Alto' " +
                        "       ELSE '-' END) AS complexidade " +
                        " , ps.ds_subgrupo AS categoriaProcedimento " +
                        " from solicitacao_agendamento sa " +
                        " left join solicitacao_agendamento_exame sae on sae.cd_solicitacao = sa.cd_solicitacao " +
                        " left join exame_procedimento ep on ep.cd_exame_procedimento = sae.cd_exame_procedimento " +
                        " left join procedimento p on p.cd_procedimento = ep.cd_procedimento " +
                        " left join tipo_procedimento tp on tp.cd_tp_procedimento = sa.cd_tp_procedimento " +
                        " left join procedimento p2 on p2.cd_procedimento = tp.cd_procedimento " +
                        " left join procedimento_competencia pc on pc.cd_procedimento = p.cd_procedimento and pc.dt_competencia = (select dt_comp_procedimento from parametro)" +
                        " left join procedimento_competencia pc2 on pc2.cd_procedimento = p2.cd_procedimento and pc2.dt_competencia = (select dt_comp_procedimento from parametro)" +
                        " left join agenda_gra_ate_horario agah on agah.cd_solicitacao = sa.cd_solicitacao and agah.status not in (3,5) and agah.cd_ag_hr_principal is null " +
                        " left join atendimento ate ON agah.nr_atendimento = ate.nr_atendimento and ate.nr_atendimento = ate.nr_atendimento_principal and ate.status <> 5 " +
                        " left join classificacao_risco cr ON sa.classificacao_risco = cr.cd_classificacao_risco " +
                        " left join empresa e2 ON agah.local_agendamento = e2.empresa " +
                        " left join procedimento_subgrupo ps ON p.cd_subgrupo = ps.cd_subgrupo and p.cd_grupo = ps.cd_grupo " +
                        " where tp.tfd = 'N' " +
                        " and sa.cd_solicitacao in (:codSolicitacoes) ";
        return query;
    }


    public static void setLimitOffsetParams(Query nativeQuery, FilaAgendamentoParamDTO param, CelkDataSourceLimits dataSourceLimits) {
        nativeQuery.setParameter("offset", (param.getPageNumber() - 1) * dataSourceLimits.getListAppointmentsLimit());
        nativeQuery.setParameter("limit", dataSourceLimits.getListAppointmentsLimit());
    }

    public static void setDateParams(Query nativeQuery, Date startDate) {
        nativeQuery.setParameter("startDate", startDate);
    }

    public static void setCodSolicitacoes(Query nativeQuery, Long codSolicitacoes) {
        nativeQuery.setParameter("codSolicitacoes", codSolicitacoes);
    }

    public static void setCodSolicitacoes(Query nativeQuery, List<Long> codSolicitacoes) {
        if (!CollectionUtils.isNullOrEmpty(codSolicitacoes)) {
            nativeQuery.setParameter("codSolicitacoes", codSolicitacoes);
        }
    }
}