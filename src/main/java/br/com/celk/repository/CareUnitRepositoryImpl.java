package br.com.celk.repository;

import br.com.celk.model.CareUnit;
import br.com.celk.web.dto.params.PageParamDTO;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CareUnitRepositoryImpl extends BaseRepository {

    private final CareUnitRepository careUnitRepository;

    public CareUnitRepositoryImpl(CareUnitRepository careUnitRepository) {
        this.careUnitRepository = careUnitRepository;
    }

    public List<CareUnit> listCareUnits(PageParamDTO pageParamDTO) {
        getRequestContext().setUsaConexaoDiretaComBanco(true);

        Pageable pageable = PageRequest.of(
            pageParamDTO.getQueryPageNumber(), 
            dataSourceLimits.getListCareUnitLimit(), 
            Sort.by("careUnitId").ascending()
        );
        return careUnitRepository.findAll(pageable).getContent();
    }

    public long count() {
        return careUnitRepository.count();
    }
}
