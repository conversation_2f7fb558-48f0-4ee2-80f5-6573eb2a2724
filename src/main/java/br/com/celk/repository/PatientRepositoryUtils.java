package br.com.celk.repository;

import br.com.celk.config.exception.RestException;
import br.com.celk.model.enuns.PatientStatusTeam;
import br.com.celk.utils.FormattingUtils;
import br.com.celk.web.dto.params.PatientParamV1DTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Query;
import javax.ws.rs.core.Response.Status;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.stream.Collectors;

public class PatientRepositoryUtils {

    private PatientRepositoryUtils() { }

    public static String getListPatientsQuery(PatientParamV1DTO patientParamDTO) {

        return "select " +
               "    case when usuario_cadsus.cd_equipe is not null then empresa2.descricao else empresa1.descricao end as careUnit, " +
               "    case when usuario_cadsus.cd_equipe is not null then equipe_area2.cd_area else equipe_area1.cd_area end as team, " +
               "    usuario_cadsus.cd_usu_cadsus as id, " +
               "    case when usuario_cadsus.cpf is not null and usuario_cadsus.cpf <> '' then usuario_cadsus.cpf else null end as cpf, " +
               "    dom_usuario_cadsus.cns as cns, " +
               "    usuario_cadsus.nm_usuario as name, " +
               "    to_char(usuario_cadsus.dt_nascimento, 'dd/mm/yyyy') as birthdate, " +
               "    usuario_cadsus.sg_sexo as gender, " +
               "    case when upper(endereco_usuario_cadsus.nm_comp_logradouro) is null then concat(tipo_logradouro_cadsus.ds_tipo_logradouro, ' ', endereco_usuario_cadsus.nm_logradouro, ', ', endereco_usuario_cadsus.nr_logradouro) else concat(tipo_logradouro_cadsus.ds_tipo_logradouro, ' ', endereco_usuario_cadsus.nm_logradouro, ', ', endereco_usuario_cadsus.nr_logradouro, ' - ', upper(endereco_usuario_cadsus.nm_comp_logradouro)) end as address, " +
               "    usuario_cadsus.nm_mae as motherName, " +
               "    usuario_cadsus.email, " +
               "    endereco_usuario_cadsus.cep as zipCode, " +
               "    endereco_usuario_cadsus.nm_bairro as district, " +
               "    coalesce(cidade1.descricao, cidade2.descricao) as city, " +
               "    estado.descricao as state, " +
               "    usuario_cadsus.nr_telefone as phone1, " +
               "    usuario_cadsus.nr_telefone_2 as phone2, " +
               "    usuario_cadsus.situacao as status, " +
               "    usuario_cadsus.apelido as socialName " +
               getFrom() +
               getWhere(patientParamDTO) +
               getOrderBy() +
               getLimitOffset();
    }


    public static String getCountPatientsQuery(PatientParamV1DTO patientParamDTO) {

        return "select " +
               "    count(usuario_cadsus.cd_usu_cadsus) as total " +
                getFrom() +
                getWhere(patientParamDTO);
    }

    private static String getFrom() {
        return "from " +
               "    dom_usuario_cadsus " +
               "left outer join usuario_cadsus on usuario_cadsus.cd_usu_cadsus = dom_usuario_cadsus.cd_usu_cadsus " +
               "left join endereco_usuario_cadsus on endereco_usuario_cadsus.cd_endereco = usuario_cadsus.cd_endereco " +
               "left join usuario_cadsus_cns on usuario_cadsus_cns.cd_usu_cadsus = usuario_cadsus.cd_usu_cadsus and usuario_cadsus_cns.st_excluido = :notDeleted " +
               "left join tipo_logradouro_cadsus on tipo_logradouro_cadsus.cd_tipo_logradouro = endereco_usuario_cadsus.cd_tipo_logradouro " +
               "left join cidade cidade1 on cidade1.cod_cid = endereco_usuario_cadsus.cod_cid " +
               "left join estado on cidade1.cod_est = estado.cod_est " +
               "left join endereco_estruturado on endereco_estruturado.cd_endereco_estruturado = endereco_usuario_cadsus.cd_endereco_estruturado " +
               "left join cidade_bairro on cidade_bairro.cd_bairro = endereco_estruturado.cd_bairro " +
               "left join cidade cidade2 on cidade2.cod_cid = cidade_bairro.cod_cid " +
               "left join equipe_micro_area on equipe_micro_area.cd_eqp_micro_area = endereco_estruturado.cd_eqp_micro_area " +
               "left join equipe equipe1 on equipe1.cd_equipe_area = equipe_micro_area.cd_equipe_area " +
               "left join equipe_area equipe_area1 on equipe_area1.cd_equipe_area = equipe1.cd_equipe_area " +
               "left join empresa empresa1 on empresa1.empresa = equipe1.empresa " +
               "left join equipe equipe2 on equipe2.cd_equipe_area = usuario_cadsus.cd_equipe " +
               "left join equipe_area equipe_area2 on equipe_area2.cd_equipe_area = equipe2.cd_equipe_area " +
               "left join empresa empresa2 on empresa2.empresa = equipe2.empresa ";
    }

    private static String getWhere(PatientParamV1DTO patientParamDTO) {
        return "where 1 = 1" +
               (PatientStatusTeam.isOnlyPatientsWithAssociatedTeam(patientParamDTO.getPatientStatusTeam()) ? "and (case when usuario_cadsus.cd_equipe is not null then equipe_area2.cd_area else equipe_area1.cd_area end) is not null " : " ") +
               (PatientStatusTeam.isOnlyPatientsWithoutAssociatedTeam(patientParamDTO.getPatientStatusTeam()) ? "and (case when usuario_cadsus.cd_equipe is not null then equipe_area2.cd_area else equipe_area1.cd_area end) is null " : " ") +
               (patientParamDTO.getCityName() != null ? "and (cidade1.descricao like :cityName or cidade2.descricao like :cityName) " : " ") +
               (patientParamDTO.getName() != null ? "and dom_usuario_cadsus.nome_referencia like :name " : " ") +
               (patientParamDTO.getMotherName() != null ? "and dom_usuario_cadsus.nome_mae like :motherName " : " ") +
               (patientParamDTO.getCpf() != null ? "and usuario_cadsus.cpf = :cpf " : " ") +
               (patientParamDTO.getCns() != null ? "and usuario_cadsus_cns.cd_numero_cartao = :cns " : " ") +
               (patientParamDTO.getBirthdate() != null ? "and dom_usuario_cadsus.dt_nascimento = :birthdate " : " ") +
               (!CollectionUtils.isEmpty(patientParamDTO.getStatus()) ? "and dom_usuario_cadsus.situacao in :status " : " ");
    }

    private static String getOrderBy() {
        return "order by  " +
               "    usuario_cadsus.cd_usu_cadsus ";
    }

    private static String getLimitOffset() {
        return "limit :limit " +
               "offset :offset";
    }

    public static void setListPatientsQueryLimitOffsetParams(Query nativeQuery, PatientParamV1DTO patientParamDTO) {
        nativeQuery.setParameter("offset", Long.parseLong(patientParamDTO.getOffset()));
        nativeQuery.setParameter("limit", patientParamDTO.getListPatientsLimit());
    }

    public static void setListPatientsQueryParams(Query nativeQuery, PatientParamV1DTO patientParamDTO) throws RestException {

        final Long NOT_DELETED = 0L;
        nativeQuery.setParameter("notDeleted", NOT_DELETED);

        if (patientParamDTO.getCityName() != null) {
            nativeQuery
                .setParameter("cityName", StringUtils.stripAccents(patientParamDTO.getCityName().toUpperCase()) + "%");
        }
        if (patientParamDTO.getName() != null) {
            nativeQuery.setParameter("name", patientParamDTO.getName().toUpperCase() + "%");
        }
        if (patientParamDTO.getMotherName() != null) {
            nativeQuery.setParameter("motherName", patientParamDTO.getMotherName().toUpperCase() + "%");
        }
        if (patientParamDTO.getCpf() != null) {
            nativeQuery.setParameter("cpf", FormattingUtils.getOnlyNumerics(patientParamDTO.getCpf()));
        }
        if (patientParamDTO.getCns() != null) {
            try {
                nativeQuery
                    .setParameter("cns", Long.parseLong(FormattingUtils.getOnlyNumerics(patientParamDTO.getCns())));
            } catch (DateTimeParseException e) {
                throw new RestException(Status.BAD_REQUEST, "CNS format is invalid");
            }
        }
        if (patientParamDTO.getBirthdate() != null) {
            try {
                nativeQuery.setParameter("birthdate",
                    LocalDate.parse(patientParamDTO.getBirthdate(), DateTimeFormatter.ofPattern("dd-MM-yyyy")));
            } catch (DateTimeParseException e) {
                throw new RestException(Status.BAD_REQUEST, "Birthdate invalid (Valid format: dd-MM-yyyy)");
            }
        }
        if (!CollectionUtils.isEmpty(patientParamDTO.getStatus())) {
            nativeQuery.setParameter("status",
                patientParamDTO.getStatus().stream().map(Integer::parseInt).collect(Collectors.toList()));
        }
    }
}
