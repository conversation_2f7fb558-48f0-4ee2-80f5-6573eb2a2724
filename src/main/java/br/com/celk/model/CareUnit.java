package br.com.celk.model;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "empresa")
public class CareUnit implements Serializable {

    @Id()
    @Column(name = "empresa")
    private Long careUnitId;

    @Column(name = "descricao")
    private String careUnitName;

    @Column(name = "telefone")
    private String phone;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "cod_cid", referencedColumnName = "cod_cid")
    private City city;

    @Column(name = "rua")
    private String street;

    @Column(name = "bairro")
    private String neighborhood;

    @Column(name = "numero")
    private String number;

    @Column(name = "complemento")
    private String complement;

    public Long getCareUnitId() {
        return careUnitId;
    }

    public void setCareUnitId(Long careUnitId) {
        this.careUnitId = careUnitId;
    }

    public String getCareUnitName() {
        return careUnitName;
    }

    public void setCareUnitName(String careUnitName) {
        this.careUnitName = careUnitName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public City getCity() {
        return city;
    }

    public void setCity(City city) {
        this.city = city;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getNeighborhood() {
        return neighborhood;
    }

    public void setNeighborhood(String neighborhood) {
        this.neighborhood = neighborhood;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getComplement() {
        return complement;
    }

    public void setComplement(String complement) {
        this.complement = complement;
    }

    @Override
    public String toString() {
        return "CareUnit{" +
                "careUnitId=" + careUnitId +
                ", careUnitName='" + careUnitName + '\'' +
                ", phone='" + phone + '\'' +
                ", city=" + city +
                ", street='" + street + '\'' +
                ", neighborhood='" + neighborhood + '\'' +
                ", number='" + number + '\'' +
                ", complement='" + complement + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CareUnit careUnit = (CareUnit) o;
        return Objects.equals(careUnitId, careUnit.careUnitId) &&
                Objects.equals(careUnitName, careUnit.careUnitName) &&
                Objects.equals(phone, careUnit.phone) &&
                Objects.equals(city, careUnit.city) &&
                Objects.equals(street, careUnit.street) &&
                Objects.equals(neighborhood, careUnit.neighborhood) &&
                Objects.equals(number, careUnit.number) &&
                Objects.equals(complement, careUnit.complement);
    }

    @Override
    public int hashCode() {
        return Objects.hash(careUnitId, careUnitName, phone, city, street, neighborhood, number, complement);
    }
}
