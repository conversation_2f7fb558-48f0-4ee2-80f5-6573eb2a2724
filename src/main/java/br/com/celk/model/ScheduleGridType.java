package br.com.celk.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "tipo_atendimento_agenda")
public class ScheduleGridType implements Serializable {

    @Id
    @Column(name = "cd_tipo")
    private Long scheduleGridTypeId;

    @Column(name = "ds_tipo")
    private String description;

    @Column(name = "tp_atendimento")
    private Long type;

    public Long getScheduleGridTypeId() {
        return scheduleGridTypeId;
    }

    public void setScheduleGridTypeId(Long scheduleGridTypeId) {
        this.scheduleGridTypeId = scheduleGridTypeId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "ScheduleGridType{" +
                "scheduleGridTypeId=" + scheduleGridTypeId +
                ", description=" + description +
                ", type=" + type +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ScheduleGridType that = (ScheduleGridType) o;
        return Objects.equals(scheduleGridTypeId, that.scheduleGridTypeId) &&
                Objects.equals(description, that.description) &&
                Objects.equals(type, that.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(scheduleGridTypeId, description, type);
    }
}
