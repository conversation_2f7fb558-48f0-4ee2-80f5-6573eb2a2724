package br.com.celk.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@JsonInclude()
@Table(name = "FilaGuiasPagasPrestadorResponse")
public class FilaGuiasPagasPrestador implements Serializable {

    @Id
    private String numeroPrestador;
    private String competencia;
    private String quantidadeRealizada;
    private String valorTotal;
    private String nomePrestador;
    private String codigoProcedimentoSIGTAP;
    private String descricaoProcedimentoSIGTAP;
    private String codigoProcedimentoOriginal;
    private String descricaoProcedimentoOriginal;


    public String getNumeroPrestador() {
        return numeroPrestador;
    }

    public void setNumeroPrestador(String numeroPrestador) {
        this.numeroPrestador = numeroPrestador;
    }
    public String getDescricaoProcedimentoOriginal() {
        return descricaoProcedimentoOriginal;
    }

    public void setDescricaoProcedimentoOriginal(String descricaoProcedimentoOriginal) {
        this.descricaoProcedimentoOriginal = descricaoProcedimentoOriginal;
    }

    public String getCodigoProcedimentoOriginal() {
        return codigoProcedimentoOriginal;
    }

    public void setCodigoProcedimentoOriginal(String codigoProcedimentoOriginal) {
        this.codigoProcedimentoOriginal = codigoProcedimentoOriginal;
    }

    public String getDescricaoProcedimentoSIGTAP() {
        return descricaoProcedimentoSIGTAP;
    }

    public void setDescricaoProcedimentoSIGTAP(String descricaoProcedimentoSIGTAP) {
        this.descricaoProcedimentoSIGTAP = descricaoProcedimentoSIGTAP;
    }

    public String getCodigoProcedimentoSIGTAP() {
        return codigoProcedimentoSIGTAP;
    }

    public void setCodigoProcedimentoSIGTAP(String codigoProcedimentoSIGTAP) {
        this.codigoProcedimentoSIGTAP = codigoProcedimentoSIGTAP;
    }

    public String getNomePrestador() {
        return nomePrestador;
    }

    public void setNomePrestador(String nomePrestador) {
        this.nomePrestador = nomePrestador;
    }

    public String getQuantidadeRealizada() {
        return quantidadeRealizada;
    }

    public void setQuantidadeRealizada(String quantidadeRealizada) {
        this.quantidadeRealizada = quantidadeRealizada;
    }

    public String getCompetencia() {
        return competencia;
    }

    public void setCompetencia(String competencia) {
        this.competencia = competencia;
    }


    public String getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(String valorTotal) {
        this.valorTotal = valorTotal;
    }

}