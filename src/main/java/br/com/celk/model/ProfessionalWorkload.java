package br.com.celk.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "profissional_carga_horaria")
public class ProfessionalWorkload implements Serializable {

    @Id
    @Column(name = "cd_prof_carga_horaria")
    private Long professionalWorkloadId;

    @Column(name = "empresa")
    private Long careUnitId;

    @Column(name = "cd_cbo")
    private String professionalSpecialtyId;

    @Column(name = "nr_registro")
    private String professionalRegistrationId;

    @Column(name = "cd_profissional")
    private Long professionalId;

    public Long getProfessionalWorkloadId() {
        return professionalWorkloadId;
    }

    public void setProfessionalWorkloadId(Long professionalWorkloadId) {
        this.professionalWorkloadId = professionalWorkloadId;
    }

    public Long getCareUnitId() {
        return careUnitId;
    }

    public void setCareUnitId(Long careUnitId) {
        this.careUnitId = careUnitId;
    }

    public String getProfessionalSpecialtyId() {
        return professionalSpecialtyId;
    }

    public void setProfessionalSpecialtyId(String professionalSpecialtyId) {
        this.professionalSpecialtyId = professionalSpecialtyId;
    }

    public String getProfessionalRegistrationId() {
        return professionalRegistrationId;
    }

    public void setProfessionalRegistrationId(String professionalRegistrationId) {
        this.professionalRegistrationId = professionalRegistrationId;
    }

    public Long getProfessionalId() {
        return professionalId;
    }

    public void setProfessionalId(Long professionalId) {
        this.professionalId = professionalId;
    }

    @Override
    public String toString() {
        return "ProfessionalWorkload{" +
                "professionalWorkloadId=" + professionalWorkloadId +
                ", careUnitId=" + careUnitId +
                ", professionalSpecialtyId='" + professionalSpecialtyId + '\'' +
                ", professionalRegistrationId='" + professionalRegistrationId + '\'' +
                ", professionalId=" + professionalId +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProfessionalWorkload that = (ProfessionalWorkload) o;
        return Objects.equals(professionalWorkloadId, that.professionalWorkloadId) &&
                Objects.equals(careUnitId, that.careUnitId) &&
                Objects.equals(professionalSpecialtyId, that.professionalSpecialtyId) &&
                Objects.equals(professionalRegistrationId, that.professionalRegistrationId) &&
                Objects.equals(professionalId, that.professionalId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(professionalWorkloadId, careUnitId, professionalSpecialtyId, professionalRegistrationId, professionalId);
    }
}
