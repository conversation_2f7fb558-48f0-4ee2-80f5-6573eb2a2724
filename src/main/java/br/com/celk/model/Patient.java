package br.com.celk.model;

import br.com.celk.model.enuns.StatusUsuarioCadsus;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import java.io.Serializable;
import java.util.Objects;

// @Entity tag necessary for entityManager bind query results
@Entity
@Schema(name = "PatientResponse")
public class Patient implements Serializable {

    @Id
    private Long id;
    private String cpf;
    private String name;
    private String birthdate;
    private String gender;
    private String email;
    private String address;
    private String zipCode;
    private String district;
    private String city;
    private String state;
    private String phone1;
    private String phone2;
    private String careUnit;
    private String team;
    private String cns;
    private String motherName;
    private String socialName;
    @Enumerated(EnumType.ORDINAL)
    private StatusUsuarioCadsus status;

    public StatusUsuarioCadsus getStatus() {
        return status;
    }

    public void setStatus(StatusUsuarioCadsus status) {
        this.status = status;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBirthdate() {
        return birthdate;
    }

    public void setBirthdate(String birthdate) {
        this.birthdate = birthdate;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPhone1() {
        return phone1;
    }

    public void setPhone1(String phone1) {
        this.phone1 = phone1;
    }

    public String getPhone2() {
        return phone2;
    }

    public void setPhone2(String phone2) {
        this.phone2 = phone2;
    }

    public String getCareUnit() {
        return careUnit;
    }

    public void setCareUnit(String careUnit) {
        this.careUnit = careUnit;
    }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

    public String getCns() {
        return cns;
    }

    public void setCns(String cns) {
        this.cns = cns;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public String getSocialName() {
        return socialName;
    }

    public void setSocialName(String socialName) {
        this.socialName = socialName;
    }

    @Override
    public String toString() {
        return "PatientResponse{" +
                "id=" + id +
                ", cpf='" + cpf + '\'' +
                ", name='" + name + '\'' +
                ", birthdate='" + birthdate + '\'' +
                ", gender='" + gender + '\'' +
                ", email='" + email + '\'' +
                ", address='" + address + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", district='" + district + '\'' +
                ", city='" + city + '\'' +
                ", state='" + state + '\'' +
                ", phone1='" + phone1 + '\'' +
                ", phone2='" + phone2 + '\'' +
                ", careUnit='" + careUnit + '\'' +
                ", team='" + team + '\'' +
                ", cns='" + cns + '\'' +
                ", motherName='" + motherName + '\'' +
                ", socialName='" + socialName + '\'' +
                ", status=" + status +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Patient that = (Patient) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(cpf, that.cpf) &&
                Objects.equals(name, that.name) &&
                Objects.equals(birthdate, that.birthdate) &&
                Objects.equals(gender, that.gender) &&
                Objects.equals(email, that.email) &&
                Objects.equals(address, that.address) &&
                Objects.equals(zipCode, that.zipCode) &&
                Objects.equals(district, that.district) &&
                Objects.equals(city, that.city) &&
                Objects.equals(state, that.state) &&
                Objects.equals(phone1, that.phone1) &&
                Objects.equals(phone2, that.phone2) &&
                Objects.equals(careUnit, that.careUnit) &&
                Objects.equals(team, that.team) &&
                Objects.equals(cns, that.cns) &&
                Objects.equals(motherName, that.motherName) &&
                Objects.equals(socialName, that.socialName) &&
                status == that.status;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, cpf, name, birthdate, gender, email, address, zipCode, district, city, state, phone1, phone2, careUnit, team, cns, motherName, socialName, status);
    }
}
