package br.com.celk.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@JsonInclude()
@Table(name = "procedimento")
public class Procedimento {

    @Id
    private Long id;
    private Long idProcedimento;
    private Long idSolicitacao;
    private Long idUsuarioCadsus;
    private String procedimento;
    private String dataAgendamento;
    private String dataConfirmacao;
    private String dataAtendimento;

    private String classificacaoRisco;

    private String unidadeDesejada;
    private String complexidade;
    private String categoriaProcedimento;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getIdSolicitacao() {
        return idSolicitacao;
    }

    public void setIdSolicitacao(Long idSolicitacao) {
        this.idSolicitacao = idSolicitacao;
    }

    public Long getIdUsuarioCadsus() {
        return idUsuarioCadsus;
    }

    public void setIdUsuarioCadsus(Long idUsuarioCadsus) {
        this.idUsuarioCadsus = idUsuarioCadsus;
    }

    public Long getIdProcedimento() {
        return idProcedimento;
    }

    public void setIdProcedimento(Long idProcedimento) {
        this.idProcedimento = idProcedimento;
    }

    public String getProcedimento() {
        return procedimento;
    }

    public void setProcedimento(String procedimento) {
        this.procedimento = procedimento;
    }

    public String getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(String dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public String getDataConfirmacao() {
        return dataConfirmacao;
    }

    public void setDataConfirmacao(String dataConfirmacao) {
        this.dataConfirmacao = dataConfirmacao;
    }

    public String getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(String dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public String getUnidadeDesejada() {
        return unidadeDesejada;
    }

    public void setUnidadeDesejada(String unidadeDesejada) {
        this.unidadeDesejada = unidadeDesejada;
    }

    public String getComplexidade() {
        return complexidade;
    }

    public void setComplexidade(String complexidade) {
        this.complexidade = complexidade;
    }

    public String getCategoriaProcedimento() {
        return categoriaProcedimento;
    }

    public void setCategoriaProcedimento(String categoriaProcedimento) {
        this.categoriaProcedimento = categoriaProcedimento;
    }


    public String getClassificacaoRisco() {
        return classificacaoRisco;
    }

    public void setClassificacaoRisco(String classificacaoRisco) {
        this.classificacaoRisco = classificacaoRisco;
    }

    @Override
    public String toString() {
        return " Procedimento{" + System.lineSeparator() +
                "idSolicitacao=" + idSolicitacao + System.lineSeparator() +
                ", idUsuarioCadsus=" + idUsuarioCadsus + System.lineSeparator() +
                ", idProcedimento=" + idProcedimento + System.lineSeparator() +
                ", procedimento='" + procedimento + '\'' + System.lineSeparator() +
                ", dataAgendamento='" + dataAgendamento + '\'' + System.lineSeparator() +
                ", dataConfirmacao='" + dataConfirmacao + '\'' + System.lineSeparator() +
                ", dataAtendimento='" + dataAtendimento + '\'' + System.lineSeparator() +
                ", classificacaoRisco='" + classificacaoRisco + '\'' + System.lineSeparator() +
                ", unidadeDesejada='" + unidadeDesejada + '\'' + System.lineSeparator() +
                ", complexidade='" + complexidade + '\'' + System.lineSeparator() +
                ", categoriaProcedimento='" + categoriaProcedimento + '\'' + System.lineSeparator() +
                '}' + System.lineSeparator();
    }
}
