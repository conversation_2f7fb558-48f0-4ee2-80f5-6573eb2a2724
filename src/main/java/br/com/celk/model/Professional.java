package br.com.celk.model;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "profissional")
public class Professional implements Serializable {

    @Id
    @Column(name = "cd_profissional")
    private Long professionalId;

    @Column(name = "nm_profissional")
    private String professionalName;

    @OneToMany(mappedBy = "professionalId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ProfessionalWorkload> professionalWorkloads;

    public Long getProfessionalId() {
        return professionalId;
    }

    public void setProfessionalId(Long professionalId) {
        this.professionalId = professionalId;
    }

    public String getProfessionalName() {
        return professionalName;
    }

    public void setProfessionalName(String professionalName) {
        this.professionalName = professionalName;
    }

    public List<ProfessionalWorkload> getProfessionalWorkloads() {
        return professionalWorkloads;
    }

    public void setProfessionalWorkloads(List<ProfessionalWorkload> professionalWorkloads) {
        this.professionalWorkloads = professionalWorkloads;
    }

    @Override
    public String toString() {
        return "Professional{" +
                "professionalId=" + professionalId +
                ", professionalName='" + professionalName + '\'' +
                ", professionalWorkloads=" + professionalWorkloads +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Professional that = (Professional) o;
        return Objects.equals(professionalId, that.professionalId) &&
                Objects.equals(professionalName, that.professionalName) &&
                Objects.equals(professionalWorkloads, that.professionalWorkloads);
    }

    @Override
    public int hashCode() {
        return Objects.hash(professionalId, professionalName, professionalWorkloads);
    }
}
