package br.com.celk.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;


@Entity
@JsonInclude()
@Table(name = "ConsultaAgendamentoResponse")
public class ConsultaAgendamento implements Serializable {

    @Id
    private Long idAgendamento;
    private Long idSolicitacao;
    private Long idPaciente;
    private String nmPaciente;
    private String dtNascimento;
    private String emailPaciente;
    private String nrTelefone1;
    private String nrTelefone2;
    private String nrTelefone3;
    private String nrTelefone4;
    private String nrCelular;
    private String dtSolicitacao;
    private String dtQueFoiAgendado;
    private String dtAgendamento;
    private String estabelecimentoInsercao;
    private String estabelecimentoExecucao;
    private String idTpProcedimento;
    private String dsTpProcedimento;
    private String procedimento;

    public Long getIdAgendamento() {
        return idAgendamento;
    }

    public void setIdAgendamento(Long idAgendamento) {
        this.idAgendamento = idAgendamento;
    }

    public Long getIdSolicitacao() {
        return idSolicitacao;
    }

    public void setIdSolicitacao(Long idSolicitacao) {
        this.idSolicitacao = idSolicitacao;
    }

    public Long getIdPaciente() {
        return idPaciente;
    }

    public void setIdPaciente(Long idPaciente) {
        this.idPaciente = idPaciente;
    }

    public String getNmPaciente() {
        return nmPaciente;
    }

    public void setNmPaciente(String nmPaciente) {
        this.nmPaciente = nmPaciente;
    }

    public String getDtNascimento() {
        return dtNascimento;
    }

    public void setDtNascimento(String dtNascimento) {
        this.dtNascimento = dtNascimento;
    }

    public String getEmailPaciente() {
        return emailPaciente;
    }

    public void setEmailPaciente(String emailPaciente) {
        this.emailPaciente = emailPaciente;
    }

    public String getNrTelefone1() {
        return nrTelefone1;
    }

    public void setNrTelefone1(String nrTelefone1) {
        this.nrTelefone1 = nrTelefone1;
    }

    public String getNrTelefone2() {
        return nrTelefone2;
    }

    public void setNrTelefone2(String nrTelefone2) {
        this.nrTelefone2 = nrTelefone2;
    }

    public String getNrTelefone3() {
        return nrTelefone3;
    }

    public void setNrTelefone3(String nrTelefone3) {
        this.nrTelefone3 = nrTelefone3;
    }

    public String getNrTelefone4() {
        return nrTelefone4;
    }

    public void setNrTelefone4(String nrTelefone4) {
        this.nrTelefone4 = nrTelefone4;
    }

    public String getNrCelular() {
        return nrCelular;
    }

    public void setNrCelular(String nrCelular) {
        this.nrCelular = nrCelular;
    }

    public String getDtSolicitacao() {
        return dtSolicitacao;
    }

    public void setDtSolicitacao(String dtSolicitacao) {
        this.dtSolicitacao = dtSolicitacao;
    }

    public String getDtQueFoiAgendado() {
        return dtQueFoiAgendado;
    }

    public void setDtQueFoiAgendado(String dtQueFoiAgendado) {
        this.dtQueFoiAgendado = dtQueFoiAgendado;
    }

    public String getDtAgendamento() {
        return dtAgendamento;
    }

    public void setDtAgendamento(String dtAgendamento) {
        this.dtAgendamento = dtAgendamento;
    }

    public String getEstabelecimentoInsercao() {
        return estabelecimentoInsercao;
    }

    public void setEstabelecimentoInsercao(String estabelecimentoInsercao) {
        this.estabelecimentoInsercao = estabelecimentoInsercao;
    }

    public String getEstabelecimentoExecucao() {
        return estabelecimentoExecucao;
    }

    public void setEstabelecimentoExecucao(String estabelecimentoExecucao) {
        this.estabelecimentoExecucao = estabelecimentoExecucao;
    }

    public String getIdTpProcedimento() {
        return idTpProcedimento;
    }

    public void setIdTpProcedimento(String idTpProcedimento) {
        this.idTpProcedimento = idTpProcedimento;
    }

    public String getDsTpProcedimento() {
        return dsTpProcedimento;
    }

    public void setDsTpProcedimento(String dsTpProcedimento) {
        this.dsTpProcedimento = dsTpProcedimento;
    }

    public String getProcedimento() {
        return procedimento;
    }

    public void setProcedimento(String procedimento) {
        this.procedimento = procedimento;
    }
}