package br.com.celk.model;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "agenda_grade")
public class ScheduleGrid implements Serializable {

    @Id
    @Column(name = "cd_ag_grade")
    private Long scheduleGridId;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "cd_agenda", referencedColumnName = "cd_agenda")
    private Schedule schedule;

    public Long getScheduleGridId() {
        return scheduleGridId;
    }

    public void setScheduleGridId(Long scheduleGridId) {
        this.scheduleGridId = scheduleGridId;
    }

    public Schedule getSchedule() {
        return schedule;
    }

    public void setSchedule(Schedule schedule) {
        this.schedule = schedule;
    }

    @Override
    public String toString() {
        return "ScheduleGrid{" +
                "scheduleGridId=" + scheduleGridId +
                ", schedule=" + schedule +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ScheduleGrid that = (ScheduleGrid) o;
        return Objects.equals(scheduleGridId, that.scheduleGridId) &&
                Objects.equals(schedule, that.schedule);
    }

    @Override
    public int hashCode() {
        return Objects.hash(scheduleGridId, schedule);
    }
}
