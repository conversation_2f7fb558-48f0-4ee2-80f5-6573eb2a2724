package br.com.celk.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "agenda")
public class Schedule implements Serializable {

    @Id
    @Column(name = "cd_agenda")
    private Long scheduleId;

    @Column(name = "empresa")
    private Long careUnitId;

    @Column(name = "cd_profissional")
    private Long professionalId;

    @Column(name = "cd_tp_procedimento")
    private Long procedureId;

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Long getProcedureId() {
        return procedureId;
    }

    public void setProcedureId(Long procedureId) {
        this.procedureId = procedureId;
    }

    public Long getCareUnitId() {
        return careUnitId;
    }

    public void setCareUnitId(Long careUnitId) {
        this.careUnitId = careUnitId;
    }

    public Long getProfessionalId() {
        return professionalId;
    }

    public void setProfessionalId(Long professionalId) {
        this.professionalId = professionalId;
    }

    @Override
    public String toString() {
        return "Schedule{" +
                "scheduleId=" + scheduleId +
                ", careUnitId=" + careUnitId +
                ", professionalId=" + professionalId +
                ", procedureId=" + procedureId +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Schedule schedule = (Schedule) o;
        return Objects.equals(scheduleId, schedule.scheduleId) &&
                Objects.equals(careUnitId, schedule.careUnitId) &&
                Objects.equals(professionalId, schedule.professionalId) &&
                Objects.equals(procedureId, schedule.procedureId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(scheduleId, careUnitId, professionalId, procedureId);
    }
}
