package br.com.celk.model.enuns;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.Arrays;
import java.util.Objects;

@Schema(enumeration = {"Active: 0", "Temporary: 1", "Inactive: 2", "Deleted: 3"})
public enum StatusAgendaGradeAtendimentoHorario {

    AGENDADO(1L),
    CONCLUIDO(2L),
    CANCELADO(3L),
    NAO_COMPARECEU(4L),
    REMANEJADO(5L);

    private final Long value;

    StatusAgendaGradeAtendimentoHorario(Long value) {
        this.value = value;
    }

    public static StatusAgendaGradeAtendimentoHorario valueOf(Long valor) {
        return Arrays.stream(StatusAgendaGradeAtendimentoHorario.values())
                .filter((StatusAgendaGradeAtendimentoHorario status) -> Objects.equals(status.getValue(), valor))
                .findFirst()
                .orElse(null);
    }

    public Long getValue() {
        return value;
    }
}
