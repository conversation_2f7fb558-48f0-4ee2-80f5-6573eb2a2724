package br.com.celk.model.enuns;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.Arrays;

@Schema(enumeration = {"Tipo consulta: 1", "Tipo Retorno: 2", "Tipo PPI: 3", "Tipo Reserva Tecnica: 4", "Tipo Regulação: 5", "Vaga Interna: 6", "App Cidadão: 7", "Teleagendamento Normal: 8", "Teleagendamento Retorno: 9"})
public enum AppointmentType {

    TIPO_CONSULTA(1),
    TIPO_RETORNO(2),
    TIPO_PPI(3),
    TIPO_RESERVA_TECNICA(4),
    TIPO_REGULACAO(5),
    VAGA_INTERNA(6),
    APP_CIDADAO(7),
    TELEAGENDAMENTO_NORMAL(8),
    TELEAGENDAMENTO_RETORNO(9);

    private final int value;

    AppointmentType(int value) {
        this.value = value;
    }

    public static AppointmentType valueOf(int value) {
        return Arrays.stream(AppointmentType.values())
                .filter((AppointmentType situacao) -> situacao.getValue() == value)
                .findFirst()
                .orElse(null);
    }

    public int getValue() {
        return value;
    }
}
