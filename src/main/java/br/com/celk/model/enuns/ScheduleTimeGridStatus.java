package br.com.celk.model.enuns;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.Arrays;

@Schema(enumeration = {"Agendado: 0", "Pendente: 1", "Bloqueado: 2", "Reservado: 3"})
public enum ScheduleTimeGridStatus {

    AGENDADO(0),
    PENDENTE(1),
    BLOQUEADO(2),
    RESERVADO(3);

    private final int value;

    ScheduleTimeGridStatus(int value) {
        this.value = value;
    }

    public static ScheduleTimeGridStatus valueOf(int value) {
        return Arrays.stream(ScheduleTimeGridStatus.values())
                .filter((ScheduleTimeGridStatus situacao) -> situacao.getValue() == value)
                .findFirst()
                .orElse(null);
    }

    public int getValue() {
        return value;
    }
}
