package br.com.celk.model.enuns;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.Arrays;
import java.util.Objects;

@Schema(enumeration = {"Active: 0", "Temporary: 1", "Inactive: 2", "Deleted: 3"})
public enum StatusTipoAtendimento {


    TIPO_CONSULTA(1L),
    TIPO_RETORNO(2L),
    TIPO_PPI(3L),
    TIPO_RESERVA_TECNICA(4L),
    TIPO_REGULACAO(5L),
    VAGA_INTERNA(6L),
    APP_CIDADAO(7L),
    TELEAGENDAMENTO_NORMAL(8L),
    TELEAGENDAMENTO_RETORNO(9L),
    TIPO_EXAME(10L),
    APP_CIDADAO_GERAL(11L);

    private final Long value;

    StatusTipoAtendimento(Long value) {
        this.value = value;
    }

    public static StatusTipoAtendimento valueOf(Long valor) {
        return Arrays.stream(StatusTipoAtendimento.values())
                .filter((StatusTipoAtendimento status) -> Objects.equals(status.getValue(), valor))
                .findFirst()
                .orElse(null);
    }

    public Long getValue() {
        return value;
    }
}
