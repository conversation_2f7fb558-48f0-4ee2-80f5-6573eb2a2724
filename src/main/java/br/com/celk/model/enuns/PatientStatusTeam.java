package br.com.celk.model.enuns;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.Arrays;

@Schema(enumeration = {"Only Patients with associated team: 0", "Only Patients without associated team: 1"})
public enum PatientStatusTeam {

    ONLY_PATIENTS_WITH_ASSOCIATED_TEAM(0),
    ONLY_PATIENTS_WITHOUT_ASSOCIATED_TEAM(1);

    private final int value;

    PatientStatusTeam(int value) {
        this.value = value;
    }

    public static PatientStatusTeam valueOf(int value) {
        return Arrays.stream(PatientStatusTeam.values())
                .filter((PatientStatusTeam patientStatusTeam) -> patientStatusTeam.getValue() == value)
                .findFirst()
                .orElse(null);
    }

    public int getValue() {
        return value;
    }

    public static boolean isOnlyPatientsWithAssociatedTeam(String valor) {
        return valor != null && PatientStatusTeam.ONLY_PATIENTS_WITH_ASSOCIATED_TEAM.getValue() == Integer.parseInt(valor);
    }

    public static boolean isOnlyPatientsWithoutAssociatedTeam(String valor) {
        return valor != null && PatientStatusTeam.ONLY_PATIENTS_WITHOUT_ASSOCIATED_TEAM.getValue() == Integer.parseInt(valor);
    }
}
