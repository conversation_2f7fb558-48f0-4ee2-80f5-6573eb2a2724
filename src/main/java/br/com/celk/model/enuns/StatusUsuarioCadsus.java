package br.com.celk.model.enuns;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.Arrays;

@Schema(enumeration = {"Active: 0", "Temporary: 1", "Inactive: 2", "Deleted: 3"})
public enum StatusUsuarioCadsus {

    ACTIVE(0),
    TEMPORARY(1),
    INACTIVE(2),
    DELETED(3);

    private final int value;

    StatusUsuarioCadsus(int value) {
        this.value = value;
    }

    public static StatusUsuarioCadsus valueOf(int valor) {
        return Arrays.stream(StatusUsuarioCadsus.values())
                .filter((StatusUsuarioCadsus status) -> status.getValue() == valor)
                .findFirst()
                .orElse(null);
    }

    public int getValue() {
        return value;
    }
}
