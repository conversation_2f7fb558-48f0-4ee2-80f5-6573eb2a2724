package br.com.celk.model.enuns;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.Arrays;
import java.util.Objects;

@Schema(enumeration = {"Active: 0", "Temporary: 1", "Inactive: 2", "Deleted: 3"})
public enum StatusSolicitacaoAgendamento {


    STATUS_REGULACAO_PENDENTE(0L),
    STATUS_FILA_ESPERA (1L),
    STATUS_AGENDADO ( 2L),
    STATUS_REGULACAO_NEGADO ( 3L),
    STATUS_REGULACAO_DEVOLVIDO ( 4L),
    STATUS_REGULACAO_AGENDADO ( 5L),
    STATUS_CANCELADO ( 6L),
    STATUS_AGENDADO_FORA_REDE ( 7L),
    STATUS_AGUARDANDO_ANALISE ( 8L),
    STATUS_DEVOLVIDO ( 9L),
    STATUS_AGUARDANDO_AUTORIZACAO ( 10L),
    STATUS_FILA_ESPERA_PRESTADOR ( 11L),
    STATUS_BLOQUEADO ( 12L),
    STATUS_REGULACAO_APROVADO (13L);

    private final Long value;

    StatusSolicitacaoAgendamento(Long value) {
        this.value = value;
    }

    public static StatusSolicitacaoAgendamento valueOf(Long valor) {
        return Arrays.stream(StatusSolicitacaoAgendamento.values())
                .filter((StatusSolicitacaoAgendamento status) -> Objects.equals(status.getValue(), valor))
                .findFirst()
                .orElse(null);
    }

    public Long getValue() {
        return value;
    }
}
