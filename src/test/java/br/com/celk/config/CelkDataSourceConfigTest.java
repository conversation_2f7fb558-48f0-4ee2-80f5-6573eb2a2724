package br.com.celk.config;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.hasValidBeanConstructor;
import static com.google.code.beanmatchers.BeanMatchers.hasValidGettersAndSetters;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

@Disabled
class CelkDataSourceConfigTest {
    @Test
    void celkDataSourceConfigShouldCreateAndReturnProperties() {
        assertThat(CelkDataSourceConfig.class, allOf(hasValidBeanConstructor(), hasValidGettersAndSetters()));
    }
}