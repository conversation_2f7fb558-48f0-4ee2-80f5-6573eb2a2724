package br.com.celk.config;

import org.junit.jupiter.api.Test;

import static org.junit.Assert.assertEquals;

class DbConnectTest {

    @Test
    void dbConectShouldCreateAndReturnProperties() {
        CelkConfig celkConfig = new CelkConfig();
        celkConfig.setPass("celk");
        celkConfig.setUser("celk");
        celkConfig.setDbname("tenant_a");
        celkConfig.setPort(8080);
        celkConfig.setUrlDatabase("localhost");

        assertEquals("*************************************************", celkConfig.getUrlEnabledTracing());
        assertEquals("celk", celkConfig.getUser());
        assertEquals("celk", celkConfig.getPass());
    }
}