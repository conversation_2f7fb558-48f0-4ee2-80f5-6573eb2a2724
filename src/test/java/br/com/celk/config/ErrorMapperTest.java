package br.com.celk.config;

import org.junit.jupiter.api.Test;

import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.Response;

import static org.junit.Assert.assertEquals;

class ErrorMapperTest {

    @Test
    void errorMapperShouldCreateResponseWithMessageAndStatus() {
        ErrorMapper errorMapper = new ErrorMapper();
        Response response = errorMapper.toResponse(new WebApplicationException("message", Response.Status.BAD_REQUEST));
        assertEquals(Response.Status.BAD_REQUEST.getStatusCode(), response.getStatus());
    }

}