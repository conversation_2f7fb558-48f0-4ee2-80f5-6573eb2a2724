package br.com.celk.service;

import br.com.celk.web.dto.CancelSchedulingDTO;
import br.com.celk.web.dto.SchedulingDTO;
import io.quarkus.test.Mock;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Alternative;
import javax.validation.Valid;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.core.Response;
import java.util.List;

@Mock
@Alternative
@ApplicationScoped
public class MockSaudeConnectService extends ServiceScheduleGridService {

    private Client client;

    @Override
    @PostConstruct
    public void intiClient() {
        this.client = ClientBuilder.newBuilder().build();
    }

    @Override
    public Response saveScheduling(@Valid List<SchedulingDTO> schedulingReserveDTOS) {
        return Response.ok().build();
    }

    @Override
    public Response cancelScheduling(@Valid List<CancelSchedulingDTO> schedulingReserveDTOS) {
        return Response.ok().build();
    }

}
