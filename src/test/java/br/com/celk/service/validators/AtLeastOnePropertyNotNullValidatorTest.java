package br.com.celk.service.validators;

import br.com.celk.web.dto.params.PatientParamV1DTO;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import javax.validation.ClockProvider;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.Annotation;

import static org.junit.jupiter.api.Assertions.assertFalse;

class AtLeastOnePropertyNotNullValidatorTest {

    @Test
    void atLeastOnePropertyNotNullValidatorShouldTreatAsInvalidIfObjectParamsDontExist() {
        AtLeastOnePropertyNotNullValidator atLeastOnePropertyNotNullValidator = new AtLeastOnePropertyNotNullValidator();
        atLeastOnePropertyNotNullValidator.initialize(getInstanciaAtLeastOnePropertyNotNull());

        assertFalse(atLeastOnePropertyNotNullValidator.isValid(new PatientParamV1DTO(), getInstanciaConstraintValidatorContext()));
    }

    @NotNull
    private ConstraintValidatorContext getInstanciaConstraintValidatorContext() {
        return new ConstraintValidatorContext() {
            @Override
            public void disableDefaultConstraintViolation() { }

            @Override
            public String getDefaultConstraintMessageTemplate() {
                return null;
            }

            @Override
            public ClockProvider getClockProvider() {
                return null;
            }

            @Override
            public ConstraintViolationBuilder buildConstraintViolationWithTemplate(String s) {
                return null;
            }

            @Override
            public <T> T unwrap(Class<T> aClass) {
                return null;
            }
        };
    }

    @NotNull
    private AtLeastOnePropertyNotNull getInstanciaAtLeastOnePropertyNotNull() {
        return new AtLeastOnePropertyNotNull() {
            @Override
            public Class<? extends Annotation> annotationType() {
                return null;
            }

            @Override
            public String message() {
                return "";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public Class<? extends Payload>[] payload() {
                return new Class[0];
            }

            @Override
            public String[] fieldNames() {
                return new String[]{};
            }
        };
    }

}