package br.com.celk.model;

import org.junit.jupiter.api.Test;

import java.time.OffsetDateTime;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

class ScheduleTimeGridTest {

    @Test
    void scheduleTimeGridShouldHaveValidPropertiesAndMethods() {
        assertThat(ScheduleTimeGrid.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSettersExcluding("persistent", "time", "entityManager"),
                hasValidBeanToStringExcluding("persistent", "time", "entityManager"),
                hasValidBeanEqualsExcluding("persistent", "time", "entityManager"),
                hasValidBeanHashCodeExcluding("persistent", "time", "entityManager")));
    }

    @Test
    void scheduleTimeGridShouldHaveValidGetterNasSetterForTime() {
        ScheduleTimeGrid ScheduleTimeGrid = new ScheduleTimeGrid();
        OffsetDateTime now = OffsetDateTime.now();
        ScheduleTimeGrid.setTime(now);

        assertEquals(now, ScheduleTimeGrid.getTime());
    }

}