package br.com.celk.model;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

class TotalCountTest {

    @Test
    void totalCountShouldHaveValidPropertiesAndMethods() {
        assertThat(TotalCount.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSetters(),
                hasValidBeanToString(),
                hasValidBeanEquals(),
                hasValidBeanHashCode()));
    }

    @Test
    void totalCountShouldHaveValidConstructorWithTotalProperty() {
        TotalCount totalCount = new TotalCount(10L);

        assertEquals(10, totalCount.getTotal().intValue());
    }

}