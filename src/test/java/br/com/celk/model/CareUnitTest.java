package br.com.celk.model;


import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

class CareUnitTest {

    @Test
    void careUnitShouldHaveValidPropertiesAndMethods() {
        assertThat(CareUnit.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSettersExcluding("persistent", "entityManager"),
                hasValidBeanToStringExcluding("persistent", "entityManager"),
                hasValidBeanEqualsExcluding("persistent", "entityManager"),
                hasValidBeanHashCodeExcluding("persistent", "entityManager")));
    }

}