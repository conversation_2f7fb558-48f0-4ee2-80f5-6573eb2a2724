package br.com.celk.model;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

class PatientTest {

    @Test
    void patientShouldHaveValidPropertiesAndMethods() {
        assertThat(Patient.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSetters(),
                hasValidBeanToString(),
                hasValidBeanEquals(),
                hasValidBeanHashCode()
        ));
    }

}