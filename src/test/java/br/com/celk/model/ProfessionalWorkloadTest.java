package br.com.celk.model;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

class ProfessionalWorkloadTest {

    @Test
    void professionalWorkloadShouldHaveValidPropertiesAndMethods() {
        assertThat(ProfessionalWorkload.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSettersExcluding("persistent"),
                hasValidBeanToStringExcluding("persistent"),
                hasValidBeanEqualsExcluding("persistent"),
                hasValidBeanHashCodeExcluding("persistent")));
    }

}