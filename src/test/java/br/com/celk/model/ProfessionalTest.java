package br.com.celk.model;


import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.*;

class ProfessionalTest {

    @Test
    void professionalShouldHaveValidPropertiesAndMethods() {
        assertThat(Professional.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSettersExcluding("persistent", "entityManager"),
                hasValidBeanToStringExcluding("persistent", "entityManager")));
    }

    @Test
    void professionalShouldHaveValidEqualsAndHashCode() {
        Professional professional = new Professional();
        Professional professional2 = new Professional();
        Professional professional3 = new Professional();
        professional3.setProfessionalId(50L);

        assertEquals(professional, professional2);
        assertEquals(professional.hashCode(), professional2.hashCode());
        assertNotEquals(professional, professional3);
        assertNotEquals(professional.hashCode(), professional3.hashCode());
    }
}