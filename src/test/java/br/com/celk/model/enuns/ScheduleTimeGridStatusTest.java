package br.com.celk.model.enuns;


import org.junit.jupiter.api.Test;

import static org.junit.Assert.assertEquals;

class ScheduleTimeGridStatusTest {

    @Test
    void scheduleTimeGridStatusShouldReturnEnumByItsValue() {
        assertEquals(ScheduleTimeGridStatus.AGENDADO, ScheduleTimeGridStatus.valueOf(0));
        assertEquals(ScheduleTimeGridStatus.PENDENTE, ScheduleTimeGridStatus.valueOf(1));
        assertEquals(ScheduleTimeGridStatus.BLOQUEADO, ScheduleTimeGridStatus.valueOf(2));
        assertEquals(ScheduleTimeGridStatus.RESERVADO, ScheduleTimeGridStatus.valueOf(3));
    }

}