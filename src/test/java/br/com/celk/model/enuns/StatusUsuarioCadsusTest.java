package br.com.celk.model.enuns;

import org.junit.jupiter.api.Test;

import static org.junit.Assert.assertEquals;

class StatusUsuarioCadsusTest {

    @Test
    void patientStatusTeamShoulReturnEnumByItsValue() {
        assertEquals(PatientStatusTeam.ONLY_PATIENTS_WITH_ASSOCIATED_TEAM, PatientStatusTeam.valueOf(0));
        assertEquals(PatientStatusTeam.ONLY_PATIENTS_WITHOUT_ASSOCIATED_TEAM, PatientStatusTeam.valueOf(1));
    }

}