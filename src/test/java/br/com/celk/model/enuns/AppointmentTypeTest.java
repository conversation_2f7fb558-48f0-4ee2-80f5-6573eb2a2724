package br.com.celk.model.enuns;


import org.junit.jupiter.api.Test;

import static org.junit.Assert.assertEquals;

class AppointmentTypeTest {

    @Test
    void appointmentTypeTestShouldReturnEnumByItsValue() {
        assertEquals(AppointmentType.TIPO_CONSULTA, AppointmentType.valueOf(1));
        assertEquals(AppointmentType.TIPO_RETORNO, AppointmentType.valueOf(2));
        assertEquals(AppointmentType.TIPO_PPI, AppointmentType.valueOf(3));
        assertEquals(AppointmentType.TIPO_RESERVA_TECNICA, AppointmentType.valueOf(4));
        assertEquals(AppointmentType.TIPO_REGULACAO, AppointmentType.valueOf(5));
        assertEquals(AppointmentType.VAGA_INTERNA, AppointmentType.valueOf(6));
        assertEquals(AppointmentType.APP_CIDADAO, AppointmentType.valueOf(7));
        assertEquals(AppointmentType.TELEAGENDAMENTO_NORMAL, AppointmentType.valueOf(8));
        assertEquals(AppointmentType.TELEAGENDAMENTO_RETORNO, AppointmentType.valueOf(9));
    }

}