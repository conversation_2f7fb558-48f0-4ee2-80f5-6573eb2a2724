package br.com.celk.model.enuns;

import org.junit.jupiter.api.Test;

import static org.junit.Assert.assertEquals;

class PatientStatusTeamTest {

    @Test
    void statusTeamShouldReturnEnumByItsValue() {
        assertEquals(StatusUsuarioCadsus.ACTIVE, StatusUsuarioCadsus.valueOf(0));
        assertEquals(StatusUsuarioCadsus.TEMPORARY, StatusUsuarioCadsus.valueOf(1));
        assertEquals(StatusUsuarioCadsus.INACTIVE, StatusUsuarioCadsus.valueOf(2));
        assertEquals(StatusUsuarioCadsus.DELETED, StatusUsuarioCadsus.valueOf(3));
    }
}