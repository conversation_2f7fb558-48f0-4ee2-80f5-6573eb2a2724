package br.com.celk.model;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

class StateTest {

    @Test
    void stateShouldHaveValidPropertiesAndMethods() {
        assertThat(State.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSetters(),
                hasValidBeanToString(),
                hasValidBeanEquals(),
                hasValidBeanHashCode()));
    }

}