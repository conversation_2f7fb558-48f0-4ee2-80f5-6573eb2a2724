package br.com.celk;

import io.quarkus.test.common.QuarkusTestResource;
import io.restassured.RestAssured;
import org.keycloak.representations.AccessTokenResponse;

@QuarkusTestResource(KeycloakServer.class)
public class KeycloakTokenUtils {

    private static final String KEYCLOAK_SERVER_URL = "http://localhost:8180/auth";
    private static final String KEYCLOAK_REALM = "quarkus";
    private static String token;

    public static String getAccessToken() {
        if (token != null) {
            return token;
        }

        token = RestAssured
            .given()
            .param("grant_type", "client_credentials")
            .param("client_id", "client_api")
            .param("client_secret", "55bed22c-e025-45f2-9055-010cfc2b5941")
            .when()
            .post(KEYCLOAK_SERVER_URL + "/realms/" + KEYCLOAK_REALM + "/protocol/openid-connect/token")
            .as(AccessTokenResponse.class).getToken();

        return token;
    }

}
