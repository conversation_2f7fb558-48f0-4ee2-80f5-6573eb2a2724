package br.com.celk;

import io.quarkus.test.common.QuarkusTestResourceLifecycleManager;
import org.testcontainers.containers.BindMode;
import org.testcontainers.containers.FixedHostPortGenericContainer;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.wait.strategy.Wait;

import java.time.Duration;
import java.util.Collections;
import java.util.Map;


public class KeycloakServer implements QuarkusTestResourceLifecycleManager {

    private GenericContainer keycloak;

    @Override
    public Map<String, String> start() {
        this.keycloak = new FixedHostPortGenericContainer(
            "quay.io/keycloak/keycloak:10.0.1")
            .withFixedExposedPort(8180, 8080)
            .withEnv("KEYCLOAK_USER", "admin")
            .withEnv("KEYCLOAK_PASSWORD", "admin")
            .withEnv("KEYCLOAK_IMPORT", "/tmp/realm.json")
            .withClasspathResourceMapping("quarkus-realm.json", "/tmp/realm.json", BindMode.READ_ONLY)
//            .withClasspathResourceMapping("celk-realm.json", "/tmp/celk-realm.json", BindMode.READ_ONLY)
            .waitingFor(Wait.forHttp("/auth"))
            .withStartupTimeout(Duration.ofMinutes(5));
        this.keycloak.start();
        return Collections.emptyMap();
    }

    @Override
    public void stop() {
        this.keycloak.stop();
    }
}
