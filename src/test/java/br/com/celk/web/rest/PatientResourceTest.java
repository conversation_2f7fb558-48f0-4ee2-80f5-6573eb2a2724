package br.com.celk.web.rest;

import br.com.celk.KeycloakTokenUtils;
import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.*;

import static io.restassured.RestAssured.given;
import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.CoreMatchers.not;

@QuarkusTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Disabled
class PatientResourceTest {

    @Test
    @Order(1)
    void shouldValidateHavingAtLeastOneParemeter() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(
                containsString(
                    "At least one of the following parameters must be specified: CPF, CNS, Name, MotherName or Birthdate")
            );
    }

    @Test
    @Order(2)
    void nameShouldHaveAtLeast5Characters() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "Raf")
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(
                containsString("Name must contain at least 5 characters")
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "Rafael")
            .get("/v1/api/patients")
            .then()
            .statusCode(200);
    }

    @Test
    @Order(3)
    void cpfShouldRespectItsFormat() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("cpf", "123456")
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(
                containsString("CPF format is invalid")
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("cpf", "394.371.670-87")
            .get("/v1/api/patients")
            .then()
            .statusCode(200);
    }

    @Test
    @Order(4)
    void cnsShouldRespectItsFormat() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("cns", "123456")
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(
                containsString("CNS format is invalid")
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("cns", "154 6804 1546 0018")
            .get("/v1/api/patients")
            .then()
            .statusCode(200);
    }

    @Test
    @Order(5)
    void birthdateShouldRespectItsFormat() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("birthdate", "20/02/2020")
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(
                containsString("Birthdate invalid (Valid format: dd-MM-yyyy)")
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("birthdate", "20-02-2020")
            .get("/v1/api/patients")
            .then()
            .statusCode(200);
    }

    @Test
    @Order(6)
    void offsetShouldBeAPositiveNumber() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("offset", "number")
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(containsString("Offset must be a integer equal or greater than 0"));

        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "Rafael")
            .queryParam("offset", "50")
            .get("/v1/api/patients")
            .then()
            .statusCode(200);
    }

    @Test
    @Order(7)
    void statusShoulBeAPositiveNumberListBetween0and3() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuar")
            .queryParam("status", "a")
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(containsString("Status must be one of the following options: 0(ACTIVE), 1(TEMPORARY), 2(INACTIVE) ou 3(DELETED)"));

        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuar")
            .queryParam("status", "-1")
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(containsString("Status must be one of the following options: 0(ACTIVE), 1(TEMPORARY), 2(INACTIVE) ou 3(DELETED)"));

        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuar")
            .queryParam("status", 0)
            .queryParam("status", 1)
            .queryParam("status", 2)
            .queryParam("status", 3)
            .queryParam("status", 4)
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(containsString("Status must be one of the following options: 0(ACTIVE), 1(TEMPORARY), 2(INACTIVE) ou 3(DELETED)"));
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuar")
            .queryParam("status", 0)
            .queryParam("status", 1)
            .get("/v1/api/patients")
            .then()
            .statusCode(200);
    }

    @Test
    @Order(8)
    void patientStatusTeamShoulBe0or1() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuar")
            .queryParam("patientStatusTeam", "a")
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(containsString("PatientStatusTeam must be one of the fallowing options: 0(ONLY_PATIENTS_WITH_ASSOCIATED_TEAM) ou 1(ONLY_PATIENTS_WITHOUT_ASSOCIATED_TEAM)"));

        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuar")
            .queryParam("patientStatusTeam", "-1")
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(containsString("PatientStatusTeam must be one of the fallowing options: 0(ONLY_PATIENTS_WITH_ASSOCIATED_TEAM) ou 1(ONLY_PATIENTS_WITHOUT_ASSOCIATED_TEAM)"));

        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuar")
            .queryParam("patientStatusTeam", "2")
            .get("/v1/api/patients")
            .then()
            .statusCode(400)
            .body(containsString("PatientStatusTeam must be one of the fallowing options: 0(ONLY_PATIENTS_WITH_ASSOCIATED_TEAM) ou 1(ONLY_PATIENTS_WITHOUT_ASSOCIATED_TEAM)"));

        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuar")
            .queryParam("patientStatusTeam", 0)
            .get("/v1/api/patients")
            .then()
            .statusCode(200);

        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuar")
            .queryParam("patientStatusTeam", 1)
            .get("/v1/api/patients")
            .then()
            .statusCode(200);
    }

    @Test
    @Order(9)
    void shouldReturnPatientResponseDTOObjectByNameParam() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuario")
            .get("/v1/api/patients")
            .then()
            .body(
                not(containsString("NOME MÃE1")),
                not(containsString("<EMAIL>")),
                not(containsString("39437167087")),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052")),
                containsString("USUARIO3"),
                containsString("<EMAIL>"),
                containsString("80629748047")
            );

        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "nome usuario1")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052"))
            );
    }

    @Test
    @Order(10)
    void shouldReturnPatientResponseDTOObjectByMotherNameParam() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("motherName", "mãe")
            .get("/v1/api/patients")
            .then()
            .body(
                not(containsString("NOME MÃE1")),
                not(containsString("<EMAIL>")),
                not(containsString("39437167087")),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052")),
                containsString("USUARIO3"),
                containsString("<EMAIL>"),
                containsString("80629748047")
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("motherName", "nome mãe1")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052"))
            );
    }

    @Test
    @Order(11)
    void shouldReturnPatientResponseDTOObjectByCPFParam() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("cpf", "39437167087")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052"))
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("cpf", "394.371.670-87")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052"))
            );
    }

    @Test
    @Order(12)
    void shouldReturnPatientResponseDTOObjectByCNSParam() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("cns", "174389628690008")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052"))
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("cns", "174 3896 2869 0008")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052"))
            );
    }

    @Test
    @Order(13)
    void shouldReturnPatientResponseDTOObjectByBirthdateParam() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("birthdate", "20-02-2020")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                containsString("NOME USUARIO2"),
                containsString("<EMAIL>"),
                containsString("24499569052"),
                not(containsString("USUARIO3")),
                not(containsString("<EMAIL>")),
                not(containsString("80629748047"))
            );
    }

    @Test
    @Order(14)
    void shouldReturnPatientResponseDTOObjectByCityNameParam() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "nome usuario")
            .queryParam("cityName", "cidade")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                containsString("NOME USUARIO2"),
                containsString("<EMAIL>"),
                containsString("24499569052")
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "nome usuario")
            .queryParam("cityName", "cidade1")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052")),
                not(containsString("usuario3")),
                not(containsString("<EMAIL>")),
                not(containsString("80629748047"))
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "nome usuario")
            .queryParam("cityName", "cidade2")
            .get("/v1/api/patients")
            .then()
            .body(
                not(containsString("NOME MÃE1")),
                not(containsString("<EMAIL>")),
                not(containsString("39437167087")),
                containsString("NOME USUARIO2"),
                containsString("<EMAIL>"),
                containsString("24499569052"),
                not(containsString("USUARIO3")),
                not(containsString("<EMAIL>")),
                not(containsString("80629748047"))
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuario")
            .queryParam("cityName", "cidade3")
            .get("/v1/api/patients")
            .then()
            .body(
                not(containsString("NOME MÃE1")),
                not(containsString("<EMAIL>")),
                not(containsString("39437167087")),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052")),
                containsString("USUARIO3"),
                containsString("<EMAIL>"),
                containsString("80629748047")
            );
    }

    @Test
    @Order(15)
    void shouldReturnPatientResponseDTOObjectByStatusParam() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "nome usuario")
            .queryParam("status", "0")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052")),
                not(containsString("USUARIO3")),
                not(containsString("<EMAIL>")),
                not(containsString("80629748047"))
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "nome usuario")
            .queryParam("status", "1")
            .get("/v1/api/patients")
            .then()
            .body(
                not(containsString("NOME MÃE1")),
                not(containsString("<EMAIL>")),
                not(containsString("39437167087")),
                containsString("NOME USUARIO2"),
                containsString("<EMAIL>"),
                containsString("24499569052"),
                not(containsString("USUARIO3")),
                not(containsString("<EMAIL>")),
                not(containsString("80629748047"))
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "usuario")
            .queryParam("status", "2")
            .get("/v1/api/patients")
            .then()
            .body(
                not(containsString("NOME mãe1")),
                not(containsString("<EMAIL>")),
                not(containsString("39437167087")),
                not(containsString("NOME USUARIO2")),
                not(containsString("<EMAIL>")),
                not(containsString("24499569052")),
                containsString("USUARIO3"),
                containsString("<EMAIL>"),
                containsString("80629748047")
            );
    }

    @Test
    @Order(16)
    void shouldReturnPatientResponseDTOObjectByOffsetParam() {
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "nome usuario")
            .queryParam("offset", "0")
            .get("/v1/api/patients")
            .then()
            .body(
                containsString("NOME MÃE1"),
                containsString("<EMAIL>"),
                containsString("39437167087"),
                containsString("NOME USUARIO2"),
                containsString("<EMAIL>"),
                containsString("24499569052")
            );
        given()
            .auth().oauth2(KeycloakTokenUtils.getAccessToken())
            .when()
            .queryParam("name", "nome usuario")
            .queryParam("offset", "1")
            .get("/v1/api/patients")
            .then()
            .body(
                not(containsString("NOME MÃE1")),
                not(containsString("<EMAIL>")),
                not(containsString("39437167087")),
                containsString("NOME USUARIO2"),
                containsString("<EMAIL>"),
                containsString("24499569052")
            );
    }
}