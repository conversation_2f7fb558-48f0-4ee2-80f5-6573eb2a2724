package br.com.celk.web.rest;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.beans.factory.annotation.Autowired;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Disabled
class CareUnitResourceTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    void careUnitResourceShouldValidatePageNumber() throws Exception {
        mockMvc.perform(get("/csaude/v1/careUnits")
                .param("pageNumber", "0"))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Page Number should be greater than 0")));

        mockMvc.perform(get("/csaude/v1/careUnits")
                .param("pageNumber", "1"))
                .andExpect(status().isOk());
    }

    @Test
    void careUnitResourceShouldReturnCareUnits() throws Exception {
        mockMvc.perform(get("/csaude/v1/careUnits")
                .param("pageNumber", "1"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("descrição empresa1")));
    }

    @Test
    void careUnitResourceShouldPaginateResponse() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 2)
                .get("/csaude/v1/careUnits")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":[]"));
    }

    @Test
    void careUnitResourceShouldReturnCareUnitsCount() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .get("/csaude/v1/careUnits/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":3"));
    }

}