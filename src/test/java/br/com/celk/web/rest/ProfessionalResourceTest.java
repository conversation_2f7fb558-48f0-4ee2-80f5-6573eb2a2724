package br.com.celk.web.rest;

import br.com.celk.KeycloakTokenUtils;
import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.hamcrest.CoreMatchers.containsString;

@QuarkusTest
@Disabled
class ProfessionalResourceTest {

    @Test
    void professionalResourceShouldValidatePageNumber() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 0)
                .get("/csaude/v1/professionals")
                .then()
                .statusCode(400)
                .body(containsString("Page Number should be greater than 0"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 1)
                .get("/csaude/v1/professionals")
                .then()
                .statusCode(200);
    }

    @Test
    void professionalResourceShouldReturnProfessionals() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 1)
                .get("/csaude/v1/professionals")
                .then()
                .statusCode(200)
                .body(containsString("{\"professionalId\":1,\"professionalName\":\"professional 1\",\"professionalWorkloads\":[{\"careUnitId\":1,\"professionalId\":1,\"professionalRegistrationId\":\"123456\",\"professionalSpecialtyId\":\"223505\",\"professionalWorkloadId\":1}]}"));
    }

    @Test
    void professionalResourceShouldPaginateResponse() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 2)
                .get("/csaude/v1/professionals")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":[]"));
    }

    @Test
    void professionalResourceShouldReturnProfessionalsCount() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .get("/csaude/v1/professionals/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":3"));
    }

}