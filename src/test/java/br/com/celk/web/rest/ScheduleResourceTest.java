package br.com.celk.web.rest;

import br.com.celk.KeycloakTokenUtils;
import br.com.celk.web.dto.CancelSchedulingDTO;
import br.com.celk.web.dto.ClkGenericData;
import br.com.celk.web.dto.SchedulingDTO;
import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;

import static io.restassured.RestAssured.given;
import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.CoreMatchers.not;

@QuarkusTest
@Disabled
class ScheduleResourceTest {

    @Test
    void scheduleResourceShouldReturnSchedules() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("amountOfDays", 2)
                .queryParam("pageNumber", 1)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(
                    containsString("scheduleGridTypeId\":1"),
                    containsString("scheduleGridTypeId\":2"),
                    containsString("scheduleGridTypeId\":3")
                );
    }

    @Test
    void scheduleResourceShouldPaginateResponse() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("amountOfDays", 1)
                .queryParam("pageNumber", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":[]"));
    }

    @Test
    void scheduleResourceShouldReturnSchedulesCount() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("amountOfDays", 7)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":3"));
    }

    @Test
    void scheduleResourceShouldReturnSchedulesByAmountOfDays() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 1)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(
                    containsString("scheduleGridTypeId\":1"),
                    containsString("scheduleGridTypeId\":2"),
                    not(containsString("scheduleGridTypeId\":3"))
                );

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("amountOfDays", 1)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":2"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(
                    containsString("scheduleGridTypeId\":1"),
                    containsString("scheduleGridTypeId\":2"),
                    containsString("scheduleGridTypeId\":3")
                );

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":3"));
    }

    @Test
    void scheduleResourceShouldReturnSchedulesByProcedureId() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("procedureId", 1)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(
                    containsString("scheduleGridTypeId\":1"),
                    not(containsString("scheduleGridTypeId\":2")),
                    not(containsString("scheduleGridTypeId\":3"))
                );

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("procedureId", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":1"));
    }

    @Test
    void scheduleResourceShouldReturnSchedulesByProfessionalId() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalId", 2)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(
                    not(containsString("scheduleGridTypeId\":1")),
                    containsString("scheduleGridTypeId\":2"),
                    not(containsString("scheduleGridTypeId\":3"))
                );

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalId", 2)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":1"));
    }

    @Test
    void scheduleResourceShouldReturnSchedulesByProfessionalSpecialtyId() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalSpecialtyId", "225142")
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(
                    not(containsString("scheduleGridTypeId\":1")),
                    not(containsString("scheduleGridTypeId\":2")),
                    containsString("scheduleGridTypeId\":3")
                );

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalSpecialtyId", "225142")
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":1"));
    }

    @Test
    void scheduleResourceShouldReturnSchedulesByAppointmentTypeId() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("appointmentTypeId", 8)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(
                    containsString("scheduleGridTypeId\":1"),
                    containsString("scheduleGridTypeId\":2"),
                    not(containsString("scheduleGridTypeId\":3"))
                );

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("appointmentTypeId", 8)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":2"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("appointmentTypeId", 8)
                .queryParam("appointmentTypeId", 9)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(
                    containsString("scheduleGridTypeId\":1"),
                    containsString("scheduleGridTypeId\":2"),
                    containsString("scheduleGridTypeId\":3")
                );

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("appointmentTypeId", 8)
                .queryParam("appointmentTypeId", 9)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":3"));
    }

    @Test
    void scheduleResourceShouldReturnSchedulesByCareUnitId() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("careUnitId", 2)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(
                    not(containsString("scheduleGridTypeId\":1")),
                    containsString("scheduleGridTypeId\":2"),
                    not(containsString("scheduleGridTypeId\":3"))
                );

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("careUnitId", 2)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":1"));
    }

    @Test
    void scheduleResourceShouldReceiveAValidPageNumber() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(containsString("scheduleGridTypeId\":1"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 0)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(containsString("Page Number should be greater than 0"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", -1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(containsString("Page Number should be greater than 0"));
    }

    @Test
    void scheduleResourceShouldReceiveAValidCareUnitId() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("careUnitId", 1)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(containsString("scheduleGridTypeId\":1"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("careUnitId", -1)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(containsString("Care Unit Id should be equal or greater than 0"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("careUnitId", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":1"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("careUnitId", -1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(400)
                .body(containsString("Care Unit Id should be equal or greater than 0"));
    }

    @Test
    void scheduleResourceShouldReceiveAValidAppointmentTypeId() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("appointmentTypeId", 8)
                .queryParam("appointmentTypeId", 9)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(containsString("scheduleGridTypeId\":1"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("appointmentTypeId", 0)
                .queryParam("appointmentTypeId", -2)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(containsString("Appointment Type should between 8 and 9"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("appointmentTypeId", 8)
                .queryParam("appointmentTypeId", 9)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("data\":3"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("appointmentTypeId", 3)
                .queryParam("appointmentTypeId", 4)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(400)
                .body(containsString("Appointment Type should between 8 and 9"));
    }

    @Test
    void scheduleResourceShouldReceiveAValidProfessionalSpecialtyId() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalSpecialtyId", 322430)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(containsString("scheduleGridTypeId\":2"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalSpecialtyId", -2)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(containsString("Professional Specialty Id should be equal or greater than 0"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalSpecialtyId", 322430)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":1"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalSpecialtyId", -2)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(400)
                .body(containsString("Professional Specialty Id should be equal or greater than 0"));
    }

    @Test
    void scheduleResourceShouldReceiveAValidProfessionalId() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalId", 1)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(containsString("scheduleGridTypeId\":1"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalId", -2)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(containsString("Professional Id should be equal or greater than 0"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalId", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":1"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("professionalId", -2)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(400)
                .body(containsString("Professional Id should be equal or greater than 0"));
    }

    @Test
    void scheduleResourceShouldReceiveAValidProcedureId() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("procedureId", 1)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(containsString("scheduleGridTypeId\":1"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("procedureId", -2)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(containsString("Procedure Id should be equal or greater than 0"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("procedureId", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":1"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("procedureId", -2)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(400)
                .body(containsString("Procedure Id should be equal or greater than 0"));
    }

    @Test
    void scheduleResourceShouldReceiveAValidAmountOfDays() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("amountOfDays", 1)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(containsString("scheduleGridTypeId\":1"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("amountOfDays", -2)
                .queryParam("pageNumber", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(containsString("Amount of days should be between 1 and 30"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("amountOfDays", 1)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":2"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("amountOfDays", 0)
                .queryParam("amountOfDays", 2)
                .get("/csaude/v1/schedules/count")
                .then()
                .statusCode(400)
                .body(containsString("Amount of days should be between 1 and 30"));
    }

    @Test
    void scheduleResourcePostShouldValidateParams() {
        ClkGenericData<List<SchedulingDTO>> postObject = new ClkGenericData<>();
        List<SchedulingDTO> schedulingReserveDTOS = Collections.singletonList(new SchedulingDTO());
        postObject.setData(schedulingReserveDTOS);

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .post("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(
                    containsString("Correlation Id is required"),
                    containsString("Patient Id is required"),
                    containsString("Care Unit Id is required"),
                    containsString("Grid Schedule Time Id is required"),
                    containsString("Grid Schedule Service Id is required"),
                    containsString("Procedure Id is required")
                );

        postObject.getData().get(0).setCorrelationId(1L);
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .post("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(
                    not(containsString("Correlation Id is required")),
                    containsString("Patient Id is required"),
                    containsString("Care Unit Id is required"),
                    containsString("Grid Schedule Time Id is required"),
                    containsString("Grid Schedule Service Id is required"),
                    containsString("Procedure Id is required")
                );

        postObject.getData().get(0).setPatientId(1L);
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .post("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(
                    not(containsString("Correlation Id is required")),
                    not(containsString("Patient Id is required")),
                    containsString("Care Unit Id is required"),
                    containsString("Grid Schedule Time Id is required"),
                    containsString("Grid Schedule Service Id is required"),
                    containsString("Procedure Id is required")
                );

        postObject.getData().get(0).setCareUnitId(1L);
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .post("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(
                    not(containsString("Correlation Id is required")),
                    not(containsString("Patient Id is required")),
                    not(containsString("Care Unit Id is required")),
                    containsString("Grid Schedule Time Id is required"),
                    containsString("Grid Schedule Service Id is required"),
                    containsString("Procedure Id is required")
                );

        postObject.getData().get(0).setGridScheduleTimeId(1L);
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .post("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(
                    not(containsString("Correlation Id is required")),
                    not(containsString("Patient Id is required")),
                    not(containsString("Care Unit Id is required")),
                    not(containsString("Grid Schedule Time Id is required")),
                    containsString("Grid Schedule Service Id is required"),
                    containsString("Procedure Id is required")
                );

        postObject.getData().get(0).setGridScheduleServiceId(1L);
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .post("/csaude/v1/schedules")
                .then()
                .statusCode(400)
                .body(
                    not(containsString("Correlation Id is required")),
                    not(containsString("Patient Id is required")),
                    not(containsString("Care Unit Id is required")),
                    not(containsString("Grid Schedule Time Id is required")),
                    not(containsString("Grid Schedule Service Id is required")),
                    containsString("Procedure Id is required")
                );

        postObject.getData().get(0).setProcedureId(1L);
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .post("/csaude/v1/schedules")
                .then()
                .statusCode(200)
                .body(
                    not(containsString("Correlation Id is required")),
                    not(containsString("Patient Id is required")),
                    not(containsString("Care Unit Id is required")),
                    not(containsString("Grid Schedule Time Id is required")),
                    not(containsString("Grid Schedule Service Id is required")),
                    not(containsString("Procedure Id is required"))
                );
    }

    @Test
    void cancelScheduleResourceShouldValidateParams() {
        ClkGenericData<List<CancelSchedulingDTO>> postObject = new ClkGenericData<>();
        List<CancelSchedulingDTO> cancelSchedulingDTOS = Collections.singletonList(new CancelSchedulingDTO());
        postObject.setData(cancelSchedulingDTOS);

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .put("/csaude/v1/schedules/cancel")
                .then()
                .statusCode(400)
                .body(
                        containsString("Scheduling Id is required"),
                        containsString("Cancellation Reason is required"),
                        containsString("Correlation Id is required")
                );

        postObject.getData().get(0).setSchedulingId(1L);
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .put("/csaude/v1/schedules/cancel")
                .then()
                .statusCode(400)
                .body(
                        not(containsString("Scheduling Id is required")),
                        containsString("Cancellation Reason is required"),
                        containsString("Correlation Id is required")
                );

        postObject.getData().get(0).setCancellationReason("Cancellation Reason");
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .put("/csaude/v1/schedules/cancel")
                .then()
                .statusCode(400)
                .body(
                        not(containsString("Scheduling Id is required")),
                        not(containsString("Cancellation Reason is required")),
                        containsString("Correlation Id is required")
                );

        postObject.getData().get(0).setCorrelationId(1L);
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .body(postObject)
                .contentType("application/json")
                .put("/csaude/v1/schedules/cancel")
                .then()
                .statusCode(200)
                .body(
                        not(containsString("Scheduling Id is required")),
                        not(containsString("Correlation Id is required")),
                        not(containsString("Cancellation Reason is required"))
                );
    }
}