package br.com.celk.web.rest;

import br.com.celk.KeycloakTokenUtils;
import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.hamcrest.CoreMatchers.containsString;

@QuarkusTest
@Disabled
class ProcedureResouceTest {

    @Test
    void procedureResourceShouldValidatePageNumber() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 0)
                .get("/csaude/v1/procedures")
                .then()
                .statusCode(400)
                .body(containsString("Page Number should be greater than 0"));

        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 1)
                .get("/csaude/v1/procedures")
                .then()
                .statusCode(200);
    }

    @Test
    void careUnitResourceShouldReturnProcedures() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 1)
                .get("/csaude/v1/procedures")
                .then()
                .statusCode(200)
                .body(containsString("procedure description 2"));
    }

    @Test
    void procedureResourceShouldPaginateResponse() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("pageNumber", 2)
                .get("/csaude/v1/procedures")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":[]"));
    }

    @Test
    void procedureResourceShouldReturnProceduresCount() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .get("/csaude/v1/procedures/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":3"));
    }

}