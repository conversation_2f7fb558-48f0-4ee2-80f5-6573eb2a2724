package br.com.celk.web.rest;


import br.com.celk.KeycloakTokenUtils;
import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.hamcrest.CoreMatchers.containsString;

@QuarkusTest
@Disabled
class PatientV2ResourceTest {

    @Test
    void patientV2ResourceShouldReturnPatients() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("name", "nome usuario")
                .queryParam("pageNumber", 1)
                .get("/csaude/v1/patients")
                .then()
                .statusCode(200)
                .body(containsString("NOME USUARIO1"));
    }

    @Test
    void patientV2ResourceShouldPaginateResponse() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("name", "nome usuario")
                .queryParam("pageNumber", 2)
                .get("/csaude/v1/patients")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":[]"));
    }

    @Test
    void patientV2ResourceShouldReturnPatientsCount() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .queryParam("name", "nome usuario")
                .get("/csaude/v1/patients/count")
                .then()
                .statusCode(200)
                .body(containsString("\"data\":2"));
    }

    @Test
    void patientV2ResourceShouldValidateParams() {
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .get("/csaude/v1/patients")
                .then()
                .statusCode(400)
                .body(containsString("At least one of the following parameters must be specified: CPF, CNS, Name, MotherName or Birthdate"));
        given()
                .auth().oauth2(KeycloakTokenUtils.getAccessToken())
                .when()
                .get("/csaude/v1/patients/count")
                .then()
                .statusCode(400)
                .body(containsString("At least one of the following parameters must be specified: CPF, CNS, Name, MotherName or Birthdate"));
    }

}