package br.com.celk.web.dto;


import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.hasValidBeanConstructor;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

class PagingDTOTest {

    @Test
    void pagingDTOShouldHaveValidPropertiesAndMethods() {
        assertThat(PagingDTO.class, allOf(
                hasValidBeanConstructor()));
    }

    @Test
    void pagingDTOShouldHaveValidBuilder() {
        PagingDTO pagingDTO = new PagingDTO().setPageNumber(0)
                                         .setPageSize(20);

        assertEquals(0, pagingDTO.getPageNumber());
        assertEquals(20, pagingDTO.getPageSize());
    }

    @Test
    void pagingDTOShouldHaveValidToString() {
        assertEquals("PagingDTO{pageSize=0, pageNumber=0}", new PagingDTO().toString());
    }

}