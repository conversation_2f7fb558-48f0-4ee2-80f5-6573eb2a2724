package br.com.celk.web.dto.response;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

class ClkBatchResponseTest {

    @Test
    void clkBatchResponseShouldHaveValidPropertiesAndMethods() {
        assertThat(ClkBatchResponse.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSetters(),
                hasValidBeanToString()));
    }

}