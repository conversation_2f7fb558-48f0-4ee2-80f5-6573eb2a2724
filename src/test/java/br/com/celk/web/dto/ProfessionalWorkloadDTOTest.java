package br.com.celk.web.dto;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

class ProfessionalWorkloadDTOTest {

    @Test
    void professionalWorkloadDTOShouldHaveValidPropertiesAndMethods() {
        assertThat(ProfessionalWorkloadDTO.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSetters(),
                hasValidBeanToString()));
    }

}