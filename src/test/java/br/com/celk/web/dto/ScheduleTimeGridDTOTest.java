package br.com.celk.web.dto;

import org.junit.jupiter.api.Test;

import java.time.OffsetDateTime;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

class ScheduleTimeGridDTOTest {

    @Test
    void scheduleTimeGridDTOShouldHaveValidPropertiesAndMethods() {
        assertThat(ScheduleTimeGridDTO.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSettersExcluding("time"),
                hasValidBeanToStringExcluding("time")));
    }

    @Test
    void scheduleTimeGridDTOShouldHaveValidGetterNasSetterForTime() {
        ScheduleTimeGridDTO scheduleTimeGridDTO = new ScheduleTimeGridDTO();
        OffsetDateTime now = OffsetDateTime.now();
        scheduleTimeGridDTO.setTime(now);

        assertEquals(now, scheduleTimeGridDTO.getTime());
    }
}