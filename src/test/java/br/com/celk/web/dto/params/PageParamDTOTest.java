package br.com.celk.web.dto.params;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

class PageParamDTOTest {

    @Test
    void pageParamDTOShouldHaveValidPropertiesAndMethods() {
        assertThat(PageParamDTO.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSettersExcluding("queryPageNumber"),
                hasValidBeanToStringExcluding("queryPageNumber")));
    }

    @Test
    void pageParamDTOShouldHaveMethodsThatReturnsPageAndPageMinus1ToQuery() {
        PageParamDTO pageParamDTO = new PageParamDTO();
        pageParamDTO.setPageNumber(1);

        assertEquals(1, pageParamDTO.getPageNumber());
        assertEquals(0, pageParamDTO.getQueryPageNumber());
    }

}