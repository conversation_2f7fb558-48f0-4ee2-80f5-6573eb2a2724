package br.com.celk.web.dto.response;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.hasValidBeanConstructor;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

class ClkErrorTest {

    @Test
    void clkErrorShouldHaveValidPropertiesAndMethods() {
        assertThat(ClkError.class, allOf(hasValidBeanConstructor()));
    }

    @Test
    void clkErrorShouldHaveValidBuilder() {
        ClkError error = new ClkError().setField("field")
                                       .setMessage("message");

        assertEquals("field", error.getField());
        assertEquals("message", error.getMessage());
    }

    @Test
    void clkErrorShouldHaveValidToString() {
        assertEquals("ClkError{message='null', field='null'}", new ClkError().toString());
    }

}