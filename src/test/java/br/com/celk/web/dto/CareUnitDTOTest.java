package br.com.celk.web.dto;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

class CareUnitDTOTest {

    @Test
    void careUnitDTOShouldHaveValidPropertiesAndMethods() {
        assertThat(CareUnitDTO.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSetters(),
                hasValidBeanToString()));
    }

}