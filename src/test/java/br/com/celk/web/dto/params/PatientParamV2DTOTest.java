package br.com.celk.web.dto.params;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

class PatientParamV2DTOTest {

    @Test
    void patientParamV2DTOShouldHaveValidPropertiesAndMethods() {
        assertThat(PatientParamV2DTO.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSettersExcluding("queryPageNumber"),
                hasValidBeanToStringExcluding("queryPageNumber")));
    }

    @Test
    void patientParamV2DTOShouldHaveMethodsThatReturnsPageAndPageMinus1ToQuery() {
        PatientParamV2DTO patientParamV2DTO = new PatientParamV2DTO();
        patientParamV2DTO.setPageNumber(1);

        assertEquals(1, patientParamV2DTO.getPageNumber());
        assertEquals(0, patientParamV2DTO.getQueryPageNumber());
    }

}