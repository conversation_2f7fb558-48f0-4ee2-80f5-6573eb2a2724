package br.com.celk.web.dto.response;

import org.junit.jupiter.api.Test;

import java.util.ArrayList;

import static com.google.code.beanmatchers.BeanMatchers.hasValidBeanConstructor;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

class ClkErrorResponseTest {

    @Test
    void clkErrorResponseShouldHaveValidPropertiesAndMethods() {
        assertThat(ClkErrorResponse.class, allOf(hasValidBeanConstructor()));
    }

    @Test
    void clkErrorResponseShouldHaveValidBuilder() {
        ClkError error = new ClkError();
        ArrayList<ClkError> errors = new ArrayList<>();
        errors.add(error);

        ClkErrorResponse clkErrorResponse= new ClkErrorResponse().setErrors(errors)
                                                                 .setMessage("message");

        assertEquals(errors, clkErrorResponse.getErrors());
        assertEquals("message", clkErrorResponse.getMessage());
    }

    @Test
    void clkErrorResponseShouldHaveValidToString() {
        assertEquals("ClkErrorResponse{errors=null, message='null'}", new ClkErrorResponse().toString());
    }

}