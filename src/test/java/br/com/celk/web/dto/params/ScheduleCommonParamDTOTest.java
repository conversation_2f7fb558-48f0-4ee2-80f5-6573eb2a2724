package br.com.celk.web.dto.params;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

class ScheduleCommonParamDTOTest {

    @Test
    void scheduleCommonParamDTOShouldHaveValidPropertiesAndMethods() {
        assertThat(ScheduleCommonParamDTO.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSetters(),
                hasValidBeanToString()));
    }

}