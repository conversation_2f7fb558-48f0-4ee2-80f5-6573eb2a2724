package br.com.celk.web.dto;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

class ScheduleDTOTest {

    @Test
    void scheduleDTOShouldHaveValidPropertiesAndMethods() {
        assertThat(ScheduleDTO.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSetters(),
                hasValidBeanToString()));
    }

}