package br.com.celk.web.dto.params;

import org.junit.jupiter.api.Test;

import static com.google.code.beanmatchers.BeanMatchers.*;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertThat;

class PatientCommonParamDTOTest {

    @Test
    void patientCommonParamDTOShouldHaveValidPropertiesAndMethods() {
        assertThat(PatientCommonParamDTO.class, allOf(
                hasValidBeanConstructor(),
                hasValidGettersAndSetters(),
                hasValidBeanToString()));
    }

}