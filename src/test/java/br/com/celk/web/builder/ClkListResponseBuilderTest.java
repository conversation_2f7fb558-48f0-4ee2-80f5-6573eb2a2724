package br.com.celk.web.builder;

import br.com.celk.web.dto.PagingDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static com.google.code.beanmatchers.BeanMatchers.hasValidBeanConstructor;
import static org.hamcrest.CoreMatchers.allOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

class ClkListResponseBuilderTest {

    @Test
    void clkListResponseBuilderShouldHaveValidConstructor() {
        assertThat(ClkListResponseBuilder.class, allOf(hasValidBeanConstructor()));
    }

    @Test
    void clkListResponseBuilderShouldBeAValidBuilder() {
        List<Long> data = new ArrayList<>();
        PagingDTO pagingDTO = new PagingDTO();

        ClkPaginatedListResponse<List<Long>> clkListResponse = new ClkListResponseBuilder<List<Long>>().builder()
                                                                      .setData(data)
                                                                      .setPaging(pagingDTO)
                                                                      .build();

        assertEquals(data, clkListResponse.getData());
        assertEquals(pagingDTO, clkListResponse.getPaging());
    }

}