# Test Configuration for Spring Boot

spring:
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: password
    driver-class-name: org.h2.Driver
  
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8180/auth/realms/quarkus
          jwk-set-uri: http://localhost:8180/auth/realms/quarkus/protocol/openid-connect/certs

# Disable security for tests
management:
  endpoints:
    web:
      exposure:
        include: "*"

logging:
  level:
    br.com.celk: DEBUG
    org.springframework.security: DEBUG
