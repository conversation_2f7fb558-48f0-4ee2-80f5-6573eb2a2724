##################################################

#                  TEST WITH H2

################## CELK CONFIGS ##################
# time in seconds
celk-test: AppConfig

celk:
  datasource:
    metrics: false
    min-size: 5
    max-size: 20
    initial-size: 8
    acquisition-timeout: 5
    leak-timeout: 5
    validation-timeout: 50
    reap-timeout: 500
    auto-commit: false
    driver: org.h2.Driver

  dbconfig:
    url: ${DB_HOST:localhost}
    port: ${DB_PORT:5432}
    user: ${DB_USER:celk}
    pass: ${DB_PASSWORD:celk}
    dbname: ${DB_NAME:tenant_a}

  keycloak:
    user-realm: quarkus
    user-roles: app_cidadao
    client-credentials: client_api
    client-credentials-secret: 55bed22c-e025-45f2-9055-010cfc2b5941
    admin-credentials: admin
    admin-credentials-secret: admin

##################################################



################## TEST CONFIGS ##################
'%test':
  quarkus:
    hibernate-orm:
      multitenant: DATABASE

    ## Datasources TEST ##
    datasource:
      db-kind: h2
      jdbc:
        url: jdbc:h2:mem:master
        driver: org.h2.Driver

      ## Datasources TEST - Tenants ##
      master:
        db-kind: h2
        jdbc:
          url: jdbc:h2:mem:master
          driver: org.h2.Driver
      tenant_a:
        db-kind: h2
        jdbc:
          url: jdbc:h2:mem:tenant_a
          driver: org.h2.Driver
      tenant_b:
        db-kind: h2
        jdbc:
          url: jdbc:h2:mem:tenant_b
          driver: org.h2.Driver
    # OIDC Configuration
    oidc:
      auth-server-url: http://localhost:8180/auth/realms/quarkus
      client-id: backend-service
      credentials:
        secret: secret

    # Enable Policy Enforcement
    keycloak:
      policy-enforcer:
        enable: false
    http:
      auth:
        permission:
          authenticated:
            paths: /auth/*
            policy: authenticated
        proactive: false


    ## Flayway Test ##
    flyway:
      tenant_a:
        locations: classpath:databases/tenant_a
        migrate-at-start: true
      tenant_b:
        locations: classpath:databases/tenant_b
        migrate-at-start: true

  ## Log Level config ##
  log:
    level: DEBUG
