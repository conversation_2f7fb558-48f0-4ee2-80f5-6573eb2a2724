{"id": "quarkus", "realm": "quarkus", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": false, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "3fc80564-13ac-4e7b-9986-322f571e82bc", "name": "confidential", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}, {"id": "a88bcac0-4f8d-49d1-bf52-39576d77aa0d", "name": "read_professional", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}, {"id": "39eb64c8-66a9-4983-9c81-27ea7e2f6273", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}, {"id": "8c1abe12-62fe-4a06-ae0d-f5fb67dddbb0", "name": "admin", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}, {"id": "6d6edc51-b062-42c8-b13f-bfa77a5021c8", "name": "write_scheduling", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}, {"id": "5afce544-6a3c-495f-b805-fd737cf5081e", "name": "user", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}, {"id": "bc431d62-a80a-425b-961a-0fb3fc59006d", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}, {"id": "9388e651-0c6d-43c7-8d8a-287719f07312", "name": "read_scheduling", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}, {"id": "2e6ec27f-83be-4df5-8a85-458859301877", "name": "read_care_unit", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}, {"id": "d9b6d98f-b849-4f70-b354-3136841d893d", "name": "read_procedure", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}, {"id": "4389cc9d-15d8-4ad1-8dd2-5b8921d3daae", "name": "read_patient", "composite": false, "clientRole": false, "containerId": "quarkus", "attributes": {}}], "client": {"realm-management": [{"id": "7db1f38d-d436-4725-93fd-030a3bbe628e", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "1163b9bd-7319-4154-a25f-0101b2548d21", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "73d0a556-072b-404f-bf8e-10e2544c8c27", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "7e727e28-2095-4443-b2da-865e684f2308", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "df9e5352-f835-4467-bcaf-cb1b5f55c1ec", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "fa77909a-32a3-41ae-9983-2b92ae03080c", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "a8780507-dc72-4433-8b95-b8e4f3c37d0e", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "f7f4697a-3977-42f6-af86-9bb006cf4d04", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["impersonation", "manage-identity-providers", "view-identity-providers", "view-realm", "query-users", "manage-clients", "manage-events", "manage-realm", "view-authorization", "manage-authorization", "view-users", "create-client", "query-clients", "query-groups", "manage-users", "view-clients", "view-events", "query-realms"]}}, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "ca7dc1ce-a981-4efe-b3f0-a7192b6d3943", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "a0ab4faa-00a9-4f52-ac9f-8e764b6a8126", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "0b4ed5e0-eceb-4d81-ba05-fa67022abe59", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "c10336be-06f3-40ef-bef5-28d8c9b8a1e2", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "1a1ffadc-11d5-44ea-bac0-d94372c8ae5c", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "5ba9a1a3-9027-4531-8253-b91f6058513c", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "b4fba807-7a7e-4e3e-bd31-45703305a9e3", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "c9384254-0af3-434c-b4ed-7c94f59a8247", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "9a0022f2-bd58-4418-828c-a8e7abe3346b", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "83df8311-4366-4d22-9425-eccc343faa3f", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}, {"id": "e81bf277-047f-4bdd-afd6-59e2016c5066", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "376bd940-e50a-4495-80fc-9c6c07312748", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "backend-service": [{"id": "df147a91-6da7-4bbc-866c-f30cf99b2637", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "0ac5df91-e044-4051-bd03-106a3a5fb9cc", "attributes": {}}], "client_api": []}}, "groups": [{"id": "ec9b58d4-1780-446e-90da-5f6281ceb8d2", "name": "Flor<PERSON>a", "path": "/Floripa", "attributes": {"tenantId": ["flroripa_tenant"]}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRoles": ["uma_authorization", "read_patient", "read_professional", "uma_authorization", "write_scheduling", "offline_access", "read_scheduling", "read_care_unit", "read_procedure", "read_patient", "offline_access"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clients": [{"id": "e9cc41a2-8e35-4d5e-949e-4879880c2ddb", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "a8732cac-ae0f-44ec-b7f3-bd2c41eff13c", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/quarkus/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/auth/admin/quarkus/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "280528ca-5e96-4bb9-9fc0-20311caac32d", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "0ac5df91-e044-4051-bd03-106a3a5fb9cc", "clientId": "backend-service", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "3eac903f-c16b-4a78-a7e8-eb8f4d402b71", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"id": "8422cefe-7f42-4f3b-abad-5f06f7d4b748", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "988e47d6-2055-45eb-82d6-0b8b25c629fc", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "authorizationSettings": {"allowRemoteResourceManagement": true, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "User Resource", "ownerManagedAccess": false, "attributes": {}, "_id": "df1b74a9-3f10-499d-a581-368de48e512b", "uris": ["/api/users/*"]}, {"name": "Administration Resource", "ownerManagedAccess": false, "attributes": {}, "_id": "7124e2f1-e6dc-44b4-87ab-24b010090b97", "uris": ["/api/admin/*"]}], "policies": [{"id": "b8710fa6-160e-4de0-adf3-398c7007a0af", "name": "Any User Policy", "description": "Any user granted with the user role can access something", "type": "role", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"roles": "[{\"id\":\"user\",\"required\":false}]"}}, {"id": "fcef30b2-68b2-4b78-9f3d-9162c6cdf5cb", "name": "Only Administrators", "description": "Only administrators can access", "type": "role", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"roles": "[{\"id\":\"admin\",\"required\":false}]"}}, {"id": "3479dd56-02e9-4222-94fe-6a13cd065195", "name": "User Resource Permission", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"User Resource\"]", "applyPolicies": "[\"Any User Policy\"]"}}, {"id": "60188298-d55b-4066-b231-6a7c56ff7cc5", "name": "Administration Resource Permission", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"Administration Resource\"]", "applyPolicies": "[\"Only Administrators\"]"}}], "scopes": [], "decisionStrategy": "UNANIMOUS"}}, {"id": "5c554a39-027f-48db-bfb1-795568f61880", "clientId": "client_api", "name": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "55bed22c-e025-45f2-9055-010cfc2b5941", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "b97513c9-cfcc-46d5-b247-785ddd60b949", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"id": "c9ee443d-407b-4f92-ba04-da0ce8a04828", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "30cb39f4-449d-4c6e-a5f9-ae3429e62e48", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "aa011d6a-c20c-43d2-bd22-d7b66367d302", "name": "Tenant URL", "protocol": "openid-connect", "protocolMapper": "oidc-hardcoded-claim-mapper", "consentRequired": false, "config": {"claim.value": "tenant_a", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "tenant_name", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "tenantId", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "376bd940-e50a-4495-80fc-9c6c07312748", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "520cc3ef-2c6b-4d84-bcde-8c063241f4bd", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "c1d3bd07-0a5f-4f4f-b381-c58a7b723029", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "19920c96-a383-4f35-8ee9-27833263cf03", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "36a0adf0-6c25-419f-98d7-cdeada8661aa", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "b0c39901-5e5d-4436-b685-908bb90ea1d9", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "55b3ee1c-cbf9-4526-93d7-aa56a9c5f1cb", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "59128144-a21a-4744-bb55-e66ff0503b18", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "69351a63-7d6e-45d0-be47-088c83b20fdb", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "3f190f54-8e3a-4c82-a799-bd12ddc475b2", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "defa3480-5368-4f34-8075-49fb982b71b3", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "069ae414-9e98-4612-a3d6-e8b5a1fa841d", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "cea58e24-d0e0-4cc6-9e34-7b3bf7d6d85b", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "b7321e2e-dd8e-41cf-a527-c765155c3f78", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "1d4d3df5-7af5-488e-8477-0ad7cb74d50a", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "1a5e26d6-211e-4f8a-b696-0ea9577db25a", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "18971685-6dd7-420f-9c09-879c4f2d54d8", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "b970d96b-0156-4db0-9beb-9c84c173e619", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "50287033-df21-45c6-aa46-c3060e6f9855", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "3dc6b97e-7063-4077-98d1-0cacf9029c7b", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "3fb9391b-376c-42ef-b012-4df461c617cc", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "83f7fc4a-5386-4f86-a103-6585e138b61d", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "8ef177b3-f485-44b1-afee-1901393b00c7", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "e994cbc7-2a1a-4465-b7b7-12b35b4fe49e", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "abaa4c9e-1fa2-4b45-a1bb-b3d650de9aca", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "bf21b514-81fd-4bbe-9236-bab5fcf54561", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "254f8de4-08e7-4d3d-a87f-4b238f0f922b", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "7934bf2a-cfc3-4b2d-a5cb-287f3ed2a977", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}]}, {"id": "f3dc793d-6011-4861-b538-399dde5434c0", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "22eeabf8-a3c3-4026-a351-367f8ace7927", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "f72c1acd-c367-41b1-8646-b6bd5fff3e3f", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "cd8e589e-5fa7-4dae-bf6e-e8f6a3fd3cff", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "708b19d1-0709-4278-b5a1-bcbeec11f51a", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "25e97210-30c7-4f35-be11-407f1fa674cb", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "647b6b8e-d06a-4659-b3be-a4a00ce35ae0", "name": "tenantId", "description": "Tenant", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "b6b4b707-21a3-41ed-b8d9-cec051de9ded", "name": "tenantId", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "Flor<PERSON>a", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "Tenant <PERSON>", "jsonType.label": "String"}}]}, {"id": "52618957-a4e8-4c6f-a902-217f2c41a2fd", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "a66ddadf-312f-491f-993c-fa58685815c6", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "xXSSProtection": "1; mode=block", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "a7679218-373d-48ca-88f8-429985faeae3", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-address-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "oidc-sha256-pairwise-sub-mapper"]}}, {"id": "2ebf6f9f-4bfc-44b9-ad7c-282f2274d35b", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "552093c3-0a0a-4234-ad7c-ae660f0f0db1", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "8f27cf74-cee7-4a73-851f-982ee45157ca", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "ff570525-6c96-4500-9d73-c02e708b39de", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "b52284eb-123a-4718-aac9-857530a24a9b", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "2b8c0a6d-d5c0-4ea2-8a9c-4843d3e04ec6", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "bf59de5a-2c93-43cc-a9aa-03be0129fe53", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper"]}}], "org.keycloak.keys.KeyProvider": [{"id": "b3efd9cc-28b6-4404-82af-8a48a966b8ff", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "20460ca5-ec24-4a9b-839a-457743d3f841", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS256"]}}, {"id": "4f02d984-7a23-4ce1-8591-848a71390efe", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "e4bf1196-7c99-4def-92a9-0250d3370f95", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "6cef6e31-0084-4b8b-a657-308dbb2e204d", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "e93c7d5f-3ec9-41a7-ad7d-e3407b8ce7cc", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "adc9dd45-04d3-4f41-b0b9-864f2a7f69aa", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "39c16577-439c-49bb-91ba-d6834a2e7c2f", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "a9b6d36c-bfc0-4ca7-a8f6-e08ed7eac896", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "fe99ae9f-7794-4c9d-9c62-3c5b467c970a", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "3bfdf51f-fb6e-4222-889b-50d0446191e9", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "467b9e0b-0952-4db8-8ac7-1a6bc9b9fa95", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "d27d5b87-54da-4022-9547-ce3111bcbe22", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "46025859-b3b4-4121-9e8d-1990a6525086", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "6d41f8de-b34b-40cb-bac2-49fc62dfff5c", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "1e0eb577-49ff-4cd5-a523-9befb380704f", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "b9032a06-6889-4df2-8723-ed4dcea57563", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "ed9b168b-6137-4212-8054-a7090a6c1c95", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"_browser_header.xXSSProtection": "1; mode=block", "_browser_header.xFrameOptions": "SAMEORIGIN", "_browser_header.strictTransportSecurity": "max-age=31536000; includeSubDomains", "permanentLockout": "false", "quickLoginCheckMilliSeconds": "1000", "_browser_header.xRobotsTag": "none", "maxFailureWaitSeconds": "900", "minimumQuickLoginWaitSeconds": "60", "failureFactor": "30", "actionTokenGeneratedByUserLifespan": "300", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "offlineSessionMaxLifespan": "5184000", "actionTokenGeneratedByAdminLifespan": "43200", "_browser_header.contentSecurityPolicyReportOnly": "", "bruteForceProtected": "false", "_browser_header.contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "waitIncrementSeconds": "60", "offlineSessionMaxLifespanEnabled": "false"}, "keycloakVersion": "7.0.1", "userManagedAccessAllowed": false}