
CREATE TABLE tipo_procedimento(
    cd_tp_procedimento INT NOT NULL,
    ds_tp_procedimento VARCHAR(120) NOT NULL
);

INSERT INTO tipo_procedimento VALUES (1, 'procedure description 1');
INSERT INTO tipo_procedimento VALUES (2, 'procedure description 2');
INSERT INTO tipo_procedimento VALUES (3, 'procedure description 3');


CREATE TABLE profissional(
    cd_profissional INT NOT NULL,
    nm_profissional VARCHAR(60) NOT NULL
);

INSERT INTO profissional VALUES (1, 'professional 1');
INSERT INTO profissional VALUES (2, 'professional 2');
INSERT INTO profissional VALUES (3, 'professional 3');


CREATE TABLE profissional_carga_horaria(
    cd_prof_carga_horaria INT NOT NULL,
    empresa INT NOT NULL,
    cd_cbo VARCHAR(10) NOT NULL,
    nr_registro VARCHAR(60) NULL,
    cd_profissional INT NOT NULL
);

INSERT INTO profissional_carga_horaria VALUES (1, 1, '223505', '123456', 1);
INSERT INTO profissional_carga_horaria VALUES (2, 2, '322430', '234567', 2);
INSERT INTO profissional_carga_horaria VALUES (3, 3, '225142', '345678', 3);

CREATE TABLE agenda_grade_atendimento(
    cd_ag_gra_atendimento INT NOT NULL,
    cd_ag_grade INT NOT NULL,
    cd_tipo INT NOT NULL,
    tempo_medio INT NOT NULL
);

INSERT INTO agenda_grade_atendimento VALUES (1, 1, 1, 15);
INSERT INTO agenda_grade_atendimento VALUES (2, 2, 2, 20);
INSERT INTO agenda_grade_atendimento VALUES (3, 3, 3, 30);

CREATE TABLE agenda_grade(
    cd_ag_grade INT NOT NULL,
    data TIMESTAMP NOT NULL,
    cd_agenda INT NOT NULL
);

INSERT INTO agenda_grade VALUES (1, CURRENT_DATE, 1);
INSERT INTO agenda_grade VALUES (2, CURRENT_DATE, 2);
INSERT INTO agenda_grade VALUES (3, CURRENT_DATE + 1, 3);

CREATE TABLE agenda(
    cd_agenda INT NOT NULL,
    empresa INT NOT NULL,
    cd_profissional INT NULL,
    cd_tp_procedimento INT NOT NULL
);

INSERT INTO agenda VALUES (1, 1, 1, 1);
INSERT INTO agenda VALUES (2, 2, 2, 2);
INSERT INTO agenda VALUES (3, 3, 3, 3);

CREATE TABLE agenda_grade_horario(
    cd_agenda_horario INT NOT NULL,
    hora TIMESTAMP NOT NULL,
    status INT NULL,
    cd_ag_gra_atendimento INT NOT NULL
);

INSERT INTO agenda_grade_horario VALUES (1, now(), 1, 1);
INSERT INTO agenda_grade_horario VALUES (2, now(), 1, 1);
INSERT INTO agenda_grade_horario VALUES (3, now(), 1, 1);
INSERT INTO agenda_grade_horario VALUES (4, now(), 1, 2);
INSERT INTO agenda_grade_horario VALUES (5, now(), 1, 2);
INSERT INTO agenda_grade_horario VALUES (6, now() + 1, 1, 3);
INSERT INTO agenda_grade_horario VALUES (7, now() + 1, 1, 3);

CREATE TABLE tipo_atendimento_agenda(
    cd_tipo INT NOT NULL,
    ds_tipo VARCHAR(30) NOT NULL,
    tp_atendimento INT NOT NULL
);

INSERT INTO tipo_atendimento_agenda VALUES (1, 'description 1', 8);
INSERT INTO tipo_atendimento_agenda VALUES (2, 'description 2', 8);
INSERT INTO tipo_atendimento_agenda VALUES (3, 'description 3', 9);