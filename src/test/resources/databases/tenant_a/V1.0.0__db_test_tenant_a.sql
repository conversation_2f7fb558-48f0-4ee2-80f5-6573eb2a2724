
CREATE TABLE usuario_cadsus(
    cd_usu_cadsus INT NOT NULL,
    cd_equipe INT NULL,
    cpf VARCHAR(14) NULL,
    nm_usuario VARCHAR(70) NOT NULL,
    dt_nascimento DATE NOT NULL,
    sg_sexo CHAR(1) NOT NULL,
    nm_mae VARCHAR(70) NULL,
    email VARCHAR(100) NULL ,
    nr_telefone VARCHAR (15) NULL,
    nr_telefone_2 VARCHAR (15) NULL,
    apelido VARCHAR(50) NULL,
    cd_endereco INT NULL,
    situacao INT NOT NULL
);

CREATE TABLE endereco_usuario_cadsus(
    cd_endereco INT NOT NULL,
    cep VARCHAR(10) NULL,
    nm_logradouro VARCHAR(50) NOT NULL,
    nr_logradouro VARCHAR(7) NULL,
    nm_comp_logradouro VARCHAR(50) NULL,
    cd_tipo_logradouro INT NULL,
    cod_cid INT NULL,
    cd_endereco_estruturado INT NULL,
    nm_bairro VARCHAR(40) NULL
);

CREATE TABLE usuario_cadsus_cns(
    cd_usu_cadsus_cns INT NOT NULL,
    cd_numero_cartao BIGINT  NOT NULL,
    cd_usu_cadsus INT NOT NULL,
    st_excluido INT NOT NULL
);

CREATE TABLE tipo_logradouro_cadsus(
    cd_tipo_logradouro INT NOT NULL,
    ds_tipo_logradouro VARCHAR(72) NOT NULL
);

CREATE TABLE cidade(
    cod_cid INT NOT NULL,
    cod_est INT NOT NULL,
    descricao VARCHAR(50) NOT NULL
);

CREATE TABLE estado(
    cod_est INT NOT NULL,
    descricao VARCHAR(40) NOT NULL
);

CREATE TABLE endereco_estruturado(
    cd_endereco_estruturado INT NOT NULL,
    cd_eqp_micro_area INT NULL,
    cd_bairro INT NOT NULL
);

CREATE TABLE cidade_bairro(
    cd_bairro INT NOT NULL,
    cod_cid INT NOT NULL
);

CREATE TABLE equipe_micro_area(
    cd_eqp_micro_area INT NOT NULL,
    cd_equipe_area INT NOT NULL
);

CREATE TABLE equipe(
    cd_equipe INT NOT NULL,
    cd_equipe_area INT NOT NULL,
    empresa INT NULL
);

CREATE TABLE equipe_area(
    cd_equipe_area INT NOT NULL,
    cd_area INT NOT NULL
);

CREATE TABLE empresa(
    empresa INT NOT NULL,
    cod_cid INT NOT NULL,
    telefone VARCHAR(20) NULL,
    descricao VARCHAR(60) NOT NULL,
    rua VARCHAR(60) NULL,
    bairro VARCHAR(60) NULL,
    numero VARCHAR(20) NULL,
    complemento VARCHAR(60) NULL
);

CREATE TABLE dom_usuario_cadsus(
    cns BIGINT NULL,
    cd_usu_cadsus INT NOT NULL,
    nome_referencia VARCHAR(200) NOT NULL,
    nome_mae VARCHAR(70) NULL,
    dt_nascimento DATE  NULL,
    situacao INT NULL
);


--Usa endereco_estruturado
INSERT INTO usuario_cadsus
    (cd_usu_cadsus, cd_equipe, cpf, nm_usuario, dt_nascimento, sg_sexo, nm_mae, email, nr_telefone, nr_telefone_2, apelido, cd_endereco, situacao)
    values (1, 1, '39437167087', 'NOME USUARIO1', '2020-02-20', 'M', 'NOME MÃE1', '<EMAIL>', '11111111111', '22222222222', 'nome social1', 1, 0);
INSERT INTO dom_usuario_cadsus (cns, cd_usu_cadsus, nome_referencia, nome_mae, dt_nascimento, situacao)
    values (174389628690008, 1, 'NOME USUARIO1', 'NOME MÃE1', '2020-02-20', 0);
INSERT INTO endereco_usuario_cadsus
    (cd_endereco, cep, nm_logradouro, nr_logradouro, nm_comp_logradouro, cd_tipo_logradouro, cd_endereco_estruturado, nm_bairro)
    values (1, '11111-111', 'nome logradouro1', '1', 'nome complemento logradouro1', 1, 1, '11111111111');
INSERT INTO usuario_cadsus_cns(cd_usu_cadsus_cns, cd_numero_cartao, cd_usu_cadsus, st_excluido) values (1, 174389628690008, 1, 0);
INSERT INTO tipo_logradouro_cadsus(cd_tipo_logradouro, ds_tipo_logradouro) values (1, 'descrição tipo logradouro1');
INSERT INTO cidade(cod_cid, cod_est, descricao) values (1, 1, 'CIDADE1');
INSERT INTO estado(cod_est, descricao) values (1, 'estado1');
INSERT INTO endereco_estruturado(cd_endereco_estruturado, cd_eqp_micro_area, cd_bairro) values (1, 1, 1);
INSERT INTO cidade_bairro(cd_bairro, cod_cid) values (1, 1);
INSERT INTO equipe_micro_area(cd_eqp_micro_area, cd_equipe_area) values (1, 1);
INSERT INTO equipe(cd_equipe, cd_equipe_area, empresa) values (1, 1, 1);
INSERT INTO equipe_area(cd_equipe_area, cd_area) values (1, 1);
INSERT INTO empresa(empresa, cod_cid, telefone, descricao, rua, bairro, numero, complemento) values (1, 1, '111111111', 'descrição empresa1', 'rua1', 'bairro1', 'numero1', 'complemento1');

--Usa ambos endereços
INSERT INTO usuario_cadsus
(cd_usu_cadsus, cd_equipe, cpf, nm_usuario, dt_nascimento, sg_sexo, nm_mae, email, nr_telefone, nr_telefone_2, apelido, cd_endereco, situacao)
values (2, 2, '24499569052', 'NOME USUARIO2', '2020-02-20', 'M', 'NOME MÃE2', '<EMAIL>', '22222222222', '22222222222', 'nome social2', 2, 1);
INSERT INTO dom_usuario_cadsus (cns, cd_usu_cadsus, nome_referencia, nome_mae, dt_nascimento, situacao)
    values (718393291720002, 2, 'NOME USUARIO2', 'NOME MÃE2', '2020-02-20', 1);
INSERT INTO endereco_usuario_cadsus
(cd_endereco, cep, nm_logradouro, nr_logradouro, nm_comp_logradouro, cd_tipo_logradouro, cod_cid, cd_endereco_estruturado, nm_bairro)
values (2, '22222-222', 'nome logradouro2', '2', 'nome complemento logradouro2', 2, 2, 2, '22222222222');
INSERT INTO usuario_cadsus_cns(cd_usu_cadsus_cns, cd_numero_cartao, cd_usu_cadsus, st_excluido) values (2, 718393291720002, 2, 0);
INSERT INTO tipo_logradouro_cadsus(cd_tipo_logradouro, ds_tipo_logradouro) values (2, 'descrição tipo logradouro2');
INSERT INTO cidade(cod_cid, cod_est, descricao) values (2, 2, 'CIDADE2');
INSERT INTO estado(cod_est, descricao) values (2, 'estado2');
INSERT INTO endereco_estruturado(cd_endereco_estruturado, cd_eqp_micro_area, cd_bairro) values (2, 2, 2);
INSERT INTO cidade_bairro(cd_bairro, cod_cid) values (2, 2);
INSERT INTO equipe_micro_area(cd_eqp_micro_area, cd_equipe_area) values (2, 2);
INSERT INTO equipe(cd_equipe, cd_equipe_area, empresa) values (2, 2, 2);
INSERT INTO equipe_area(cd_equipe_area, cd_area) values (2, 2);
INSERT INTO empresa(empresa, cod_cid, telefone, descricao, rua, bairro, numero, complemento) values (2, 2, '222222222', 'descrição empresa2', 'rua2', 'bairro2', 'numero2', 'complemento2');

--Uses endereço normal
INSERT INTO usuario_cadsus
(cd_usu_cadsus, cd_equipe, cpf, nm_usuario, dt_nascimento, sg_sexo, nm_mae, email, nr_telefone, nr_telefone_2, apelido, cd_endereco, situacao)
values (3, 3, '80629748047', 'USUARIO3', '2020-01-10', 'M', 'MÃE3', '<EMAIL>', '33333333333', '33333333333', 'nome social3', 3, 2);
INSERT INTO dom_usuario_cadsus (cns, cd_usu_cadsus, nome_referencia, nome_mae, dt_nascimento, situacao)
    values (253677710910007, 3, 'USUARIO3', 'MÃE3', '2020-01-10', 2);
INSERT INTO endereco_usuario_cadsus
(cd_endereco, cep, nm_logradouro, nr_logradouro, nm_comp_logradouro, cd_tipo_logradouro, cod_cid, nm_bairro)
values (3, '33333-333', 'logradouro3', '3', 'complemento logradouro3', 3, 3, '33333333333');
INSERT INTO usuario_cadsus_cns(cd_usu_cadsus_cns, cd_numero_cartao, cd_usu_cadsus, st_excluido) values (3, 253677710910007, 3, 0);
INSERT INTO tipo_logradouro_cadsus(cd_tipo_logradouro, ds_tipo_logradouro) values (3, 'descrição tipo logradouro3');
INSERT INTO cidade(cod_cid, cod_est, descricao) values (3, 3, 'CIDADE3');
INSERT INTO estado(cod_est, descricao) values (3, 'estado3');
INSERT INTO equipe_micro_area(cd_eqp_micro_area, cd_equipe_area) values (3, 3);
INSERT INTO equipe(cd_equipe, cd_equipe_area, empresa) values (3, 3, 3);
INSERT INTO equipe_area(cd_equipe_area, cd_area) values (3, 3);
INSERT INTO empresa(empresa, cod_cid, telefone, descricao, rua, bairro, numero, complemento) values (3, 3, '333333333', 'descrição empresa3', 'rua3', 'bairro3', 'numero3', 'complemento3');