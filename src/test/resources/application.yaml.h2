##################################################

#                  TEST WITH H2

################## CELK CONFIGS ##################
# time in seconds

celk:
  datasource:
    metrics: false
    min-size: 5
    max-size: 20
    initial-size: 8
    acquisition-timeout: 5
    leak-timeout: 5
    validation-timeout: 50
    reap-timeout: 500
    auto-commit: false
    driver: org.h2.Driver


##################################################



################## TEST CONFIGS ##################
'%test':
  quarkus:
    hibernate-orm:
      multitenant: DATABASE

    ## Datasources TEST ##
    datasource:
      db-kind: h2
      jdbc:
        url: jdbc:h2:mem:master
        driver: org.h2.Driver

      ## Datasources TEST - Tenants ##
      master:
        db-kind: h2
        jdbc:
          url: jdbc:h2:mem:master
          driver: org.h2.Driver
      tenant_a:
        db-kind: h2
        jdbc:
          url: jdbc:h2:mem:tenant_a
          driver: org.h2.Driver
      tenant_b:
        db-kind: h2
        jdbc:
          url: jdbc:h2:mem:tenant_b
          driver: org.h2.Driver


    ## Flayway Test ##
    flyway:
      tenant_a:
        locations: classpath:databases/tenant_a
        migrate-at-start: true
      tenant_b:
        locations: classpath:databases/tenant_b
        migrate-at-start: true

  ## Log Level config ##
  log:
    level: DEBUG
