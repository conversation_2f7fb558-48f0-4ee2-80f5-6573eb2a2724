##################################################

#               TEST WITH POSTGRES

################## CELK CONFIGS ##################
# time in seconds

celk:
  datasource:
    metrics: false
    min-size: 5
    max-size: 20
    initial-size: 8
    acquisition-timeout: 5
    leak-timeout: 5
    validation-timeout: 50
    reap-timeout: 500
    auto-commit: false
    driver: io.opentracing.contrib.jdbc.TracingDriver


  ##################################################



  ################## TEST CONFIGS ##################
  '%test':
  quarkus:
    hibernate-orm:
      multitenant: DATABASE

    ## Datasources TEST ##
    datasource:
      db-kind: postgresql
      jdbc:
        url: ***************************************

      ## Datasources TEST - Tenants ##
      master:
        db-kind: postgresql
        jdbc:
          max-size: 1
          min-size: 1
          url: ***************************************
      tenant_a:
        db-kind: postgresql
        username: celk
        password: celk
        jdbc:
          max-size: 8
          min-size: 2
          url: *****************************************
      tenant_b:
        db-kind: postgresql
        username: celk
        password: celk
        jdbc:
          max-size: 8
          min-size: 2
          url: *****************************************

    ## Flayway Test ##
    flyway:
      tenant_a:
        locations: classpath:databases/tenant_a
        migrate-at-start: true
      tenant_b:
        locations: classpath:databases/tenant_b
        migrate-at-start: true

  ## Log Level config ##
  log:
    level: DEBUG
