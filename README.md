# Partner Data API - PDA

* Este projeto tem como objetivo ser um ponto de ENTRADA, para que parceiros da Celk possam se conectar e consumir e enviar dados ao Saúde.

> Obs.: Consumidores com acesso a dados de apenas 1 cliente são criados para terem o tenant configurado diretamente no
> usuário criado no keycloak.
> Para Consumidores onde podem ter acessoa a dados de vários clientes, o tenant correspondente ao cliente deve ser enviado
> junto ao header da requisição, com a propriedade "X-TenantID".

## Setup com ambiente Híbrido, usando Keycloak e Dynamo em Apoio
1. Instalar o SDK Man:
   ```shell
   curl -s "https://get.sdkman.io" | bash
   source "$HOME/.sdkman/bin/sdkman-init.sh"
   sdk version
   ```

2. Instalar o Java via SDK Man
- Como já, possuímos um arquivo .sdkmanrc, contendo a versão do java que deve ser usada no projeto, basta rodar
   ```shell
   sdk env install
   ```

3. Atualizar o Maven Wrapper
   ```shell
   mvn wrapper:wrapper
   ```

4. Rodar o PDA em modo desenvolimento
   ```shell
   ./mvnw quarkus:dev -Dquarkus.profile=dev
   ```

<hr/>

## Setup Ambiente Local

1. Instalar o AWS CLI v2:
   ```shell
   curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
   unzip awscliv2.zip
   sudo ./aws/install
   ```

2. Instalar o Docker 
   ```shell
   sudo snap install docker
   ```
* Para rodar o docker como usuário normal:
   ```shell
   sudo addgroup --system docker
   sudo adduser $USER docker
   newgrp docker
   sudo snap disable docker
   sudo snap enable docker
   sudo systemctl restart docker
   docker run hello-world
   ```

3. Instalar o SDK Man:
   ```shell
   curl -s "https://get.sdkman.io" | bash
   source "$HOME/.sdkman/bin/sdkman-init.sh"
   sdk version
   ```

4. Instalar o Java via SDK Man
   ```shell
   sdk env install
   ```

5. Configurar o AWS localmente:
   ```shell
   aws configure
   AWS Access Key ID [None]: testkey
   AWS Secret Access Key [None]: testsecret
   Default region name [us-east-1]: sa-east-1
   Default output format [None]:
   ```

6. Atualizar o Maven Wrapper
   ```shell
   mvn wrapper:wrapper
   ```

7. Configurar o banco DynamoDB LOCALMENTE
   * Editar o arquivo /etc/hosts e adicionar as linhas
       ```
       127.0.0.1       dynamodb
       ::1             dynamodb
       ```

   * Após, salvar o arquivo e reiniciar o serviço de hosts
       ```shell
       sudo /bin/systemctl restart systemd-hostnamed
       ```

   * Modificar o arquivo /development/migration/load_data.json e incluir um json assim:
      ```json
      {
        "PutRequest": {
          "Item": {
            "tenantName": {
              "S": "testegoiania"
            },
            "uf": {
              "S": "GO"
            },
            "idCidade": {
              "S": "520780" //select cod_cid from dom_cidade dc where dc.ds_cidade ilike 'goiania%';
            },
            "dbname": {
              "S": "teste_goiania"
            },
            "user": {
              "S": "celk"
            },
            "pass": {
              "S": "TcelkDB"
            },
            "port": {
              "S": "5432"
            },
            "urlDatabase": {
              "S": "dbteste11.celk.com.br"
            },
            "urlDatabaseReading": {
              "S": "dbteste11.celk.com.br"
            },
            "urlDomain": {
              "S": "http://algumteste.celk.com.br"
            }
          }
        }
      }
      ```

8. Rodar/parar containers do DynamoDb e fazer migrate
   ```shell
   ./development/stop.sh && ./development/start.sh
   ```

9. Rodar o PDA em modo desenvolimento
   ```shell
   ./mvnw quarkus:dev -Dquarkus.profile=dev
   ```

<hr/>

## DynamoDB na AWS de apoio:
   - Na tabela pda_database_data
   - Verificar para o tenant que você estiver testando se já existe algum registro.
     - Caso já exista clique em editar e verifique se os atributos *UF* e *idCidade* existem e estão preenchidos.
       - Caso os atributos não existam, crie novos com o tipo String.
       - Caso não exista um registro, crie um novo duplicando o registro de testegoiania já existente, e preencha com os dados:
      ```yaml
           tenantName: nometenant
           dbname: teste_algumbanco
           idCidade: preencher com o valor do retorno da query
           pass: TcelkDB
           port: 5432
           uf: preencher com o valor do retorno da query
           urlDatabase: dbteste11.celk.com.br
           urlDatabaseReading: dbteste11.celk.com.br
           urlDomain: testealgumacoisa.celk.com.br
           user: celk
      ```
     - Para preencher os atributos idCidade e UF realizar a query:
        ```sql
        select cod_cid, e.sigla
        from dom_cidade dc
        left join estado e on dc.cod_est = e.cod_est
        where dc.ds_cidade ilike 'goiania%';
        ```


<hr/>

## Keycloak de Apoio: Verificar Realms, Clients, Secrets e Roles

> Resumo básico sobre Keycloak:
>* É uma aplicação para gerenciamento de acessos.
>* Cada Realm (reino) é entendido como uma aplicação. No caso do ambiente de apoio da Celk, o Realm API é o que dá
   acesso às API's do PDA.
>* Cada Client do Keycloak é como se fosse um usuário que existe dentro de um Realm e tem ou não permissão para acessar
   determinadas coisas dentro da aplicação.
>* Essas determinações são feitas pelas ROLES. Cada Role é um papel que o usuário pode assumir, assim como um
   funcionário de uma empresa pode ter várias funções.
>* Para usar o PDA em conjunto com o Saúde, temos de usar 2 Realms, o API, para acessar o PDA e o Celk-Realm para
   acessar o Saúde.



### Como o projeto está configurado no Keycloak
> PDA -> utiliza o realm api
> * O PDA de apoio precisa estar configurado com os dados de acesso do celk-realm para que possa ter acesso a ele e enviar requisições ao Saúde.
> * Para isso, verificar se as variáveis em `application.yaml` estão assim:
>   * KEYCLOAK_URL: https://keycloak.apoio.celk.info/auth
>   * KEYCLOAK_REALM_PATH: /realms/celk-realm
>   * KEYCLOAK_OIDC_CLIENT_ID: ck_saude
>   * KEYCLOAK_OIDC_CLIENT_SECRET: fce8f8eb-71a1-4f2c-a38b-e6270b14e118
> Saúde -> utiliza o realm celk-realm

* Para acessar a aplicação do ambiente de apoio do Keycloak peça ajuda ao Everton ou Maicon:
  - https://keycloak.apoio.celk.info
  - user: pedir ao Everton ou Maicon
  - senha: pedir ao Everton ou Maicon

<hr/>

## Setup Saúde Teste

* Verificar se os dados no projeto Saúde de testeXXX.celk.com.br correspondem ao banco que você está utilizando, com o update:

```sql
UPDATE public.parametro
   SET keycloack_client_id='ck_saude',
       keycloack_client_secret='fce8f8eb-71a1-4f2c-a38b-e6270b14e118',
       keycloack_public_key='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw4hDPzHQ2c4PKD/s/4X/ci6n/YaKbT1dqhec01RO6GiFMWoZtChMTmYTzoqVW9uoWkOFlYH7lawjb0KPMVm2t2drk6mp5kUUBZkEHwsIdXCdz9aXimTjawXpBHVsDH/X2j7HGuSRchm3FNuLAsBzi1JIOtMfPKZRw2TPhVjIdhr/X6sgWMrMZuIqkdhrjMiO3ZyYgNjOz9GqkIBqHbVOx1Y0zfLPSB+gAYnGGx0Tot4CWkeoStA7+bedNKo0PMTV7Et5Tzjcpey4EDrUUWkCh88s8CWFcH0G/3zjmDXinS6sRItJ8PdZ1hsDKYZsapT96p63G1teAOX4+TCA7PXWpwIDAQAB',
       dt_atualizacao = current_timestamp;
 
update parametro_gem set valor = 'https://sma.apoio.celk.info/v1/api'
  where parametro = 'URLComunicaçãoAuthCidadão';
update parametro_gem set valor = 'https://keycloak.apoio.celk.info/realms/celk-realm'
  where parametro = 'URLKeycloackCidadao';
```

<hr/>

## Collection do POSTMAN

1. Importar a collection (PDA 2.postman_collection.json) que está no na pasta `/docs` do projeto.
2. Na Collection importada, verificar as variáveis:

Caso rodar em Ambiente **desenvolvimento e local**:
- pdaServer:      http://localhost:8383
- keycloakServer: https://keycloak.apoio.celk.info
- clientID:       #### -> dados que você criou no keycloak de apoio no Realm API
- clientSecret:   #### -> dados que você criou no keycloak de apoio no Realm API

Caso rodar em Ambiente de **homologação**:

- pdaServer:      https://pda.apoio.celk.info
- keycloakServer: https://keycloak.apoio.celk.info
- clientID:       #### -> dados que você criou no keycloak de apoio no Realm API
- clientSecret:   #### -> dados que você criou no keycloak de apoio no Realm API


3. Quando você rodar a Request POST - TOKEN, que ela irá armazenar um token gerado para o clientID e clientSecret na
   variável pdaToken na própria collection, válida por 5 minutos.



### Após Rodar o PDA, tente executar as Consultas pelo POSTMAN!!

<hr/>

### Rodar testes unitários localmente

Baixar as imagens do docker

```shell
docker pull quay.io/keycloak/keycloak:10.0.1
docker pull quay.io/testcontainers/ryuk:0.2.3
```

Link documentação para cliente:
https://celksistemas-my.sharepoint.com/:w:/r/personal/everton_celk_net/_layouts/15/Doc.aspx?sourcedoc=%7B6D1F934A-CB99-4044-99AA-A88CDD4127CB%7D&file=Integra%C3%A7%C3%A3o%20APP-GO.docx&action=default&mobileredirect=true

Manual telemedicina
https://celksistemas-my.sharepoint.com/:w:/g/personal/vanessa_santos_celk_net/ESN5dhgzqpZDkEKWDSRJPTwBjBOeQwxPdElIQXnsSO6Y_A?e=A0j9bV