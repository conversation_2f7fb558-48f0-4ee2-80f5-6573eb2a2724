{"info": {"_postman_id": "d80a12c9-953a-4603-aaec-b92378580c3a", "name": "PDA - Tenant", "description": "Collection do PDA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "19523546"}, "item": [{"name": "Paciente", "item": [{"name": "Medicamentos", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-TenantID", "value": "{{tenant}}", "type": "text"}], "url": {"raw": "{{pdaServer}}/csaude/paciente/medicamentos?cpf&cns=", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "medicamentos"], "query": [{"key": "cpf", "value": null}, {"key": "cns", "value": ""}]}}, "response": []}, {"name": "Exames", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-TenantID", "value": "{{tenant}}", "type": "text"}], "url": {"raw": "{{pdaServer}}/csaude/paciente/exames?cpf=&cns=", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "exames"], "query": [{"key": "cpf", "value": ""}, {"key": "cns", "value": ""}]}}, "response": []}, {"name": "Agendamentos", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-TenantID", "value": "{{tenant}}", "type": "text"}], "url": {"raw": "{{pdaServer}}/csaude/paciente/agendamentos?cpf=&cns", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "agendamentos"], "query": [{"key": "cpf", "value": ""}, {"key": "cns", "value": null}]}}, "response": []}, {"name": "Vacinas", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-TenantID", "value": "{{tenant}}", "type": "text"}], "url": {"raw": "{{pdaServer}}/csaude/paciente/vacinas?cpf=&cns=", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "vacinas"], "query": [{"key": "cpf", "value": ""}, {"key": "cns", "value": ""}]}}, "response": []}]}, {"name": "Agenda", "item": [{"name": "Consultar Agendas Procedimento", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-TenantID", "value": "{{tenant}}", "type": "text"}], "url": {"raw": "{{pdaServer}}/csaude/agenda/consultarAgendasProcedimento", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "agenda", "consultarAgendasProcedimento"]}}, "response": []}, {"name": "Consultar Agenda Horario", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-TenantID", "value": "{{tenant}}", "type": "text"}], "url": {"raw": "{{pdaServer}}/csaude/agenda/consultarAgendaHorario?careUnitId=&procedureId=", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "agenda", "consultarAgendaHorario"], "query": [{"key": "careUnitId", "value": ""}, {"key": "procedureId", "value": ""}]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "X-TenantID", "value": "{{tenant}}", "type": "text"}], "url": {"raw": "{{pdaServer}}/csaude/agenda/agendarPaciente?cpf=&cns=&careUnitId=&timeScheduleId=&gridScheduleServiceId=&procedureId=", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "agenda", "agendarPaciente"], "query": [{"key": "cpf", "value": ""}, {"key": "cns", "value": ""}, {"key": "careUnitId", "value": ""}, {"key": "timeScheduleId", "value": ""}, {"key": "gridScheduleServiceId", "value": ""}, {"key": "procedureId", "value": ""}]}}, "response": []}, {"name": "Cancelar Agendamento Paciente", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "X-TenantID", "value": "{{tenant}}", "type": "text"}], "url": {"raw": "{{pdaServer}}/csaude/agenda/cancelarAgendamentoPaciente?idAgenda=", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "agenda", "cancelarAgendamentoPaciente"], "query": [{"key": "idAgenda", "value": ""}]}}, "response": []}]}, {"name": "Token", "event": [{"listen": "test", "script": {"exec": ["var resp = pm.response.json();", "var token = resp.access_token;", "", "pm.collectionVariables.set(\"pdaToken\", token);"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "client_id", "value": "{{clientID}}", "type": "text"}, {"key": "client_secret", "value": "{{clientSecret}}", "type": "text"}]}, "url": {"raw": "{{keycloakServer}}/auth/realms/api/protocol/openid-connect/token", "host": ["{{keycloakServer}}"], "path": ["auth", "realms", "api", "protocol", "openid-connect", "token"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "pdaServer", "value": "https://pda.apoio.celk.info", "type": "string"}, {"key": "tenant", "value": "", "type": "string"}, {"key": "pdaToken", "value": "", "type": "string"}, {"key": "keycloakServer", "value": "https://keycloak.apoio.celk.info", "type": "string"}, {"key": "clientID", "value": "", "type": "string"}, {"key": "clientSecret", "value": "", "type": "string"}, {"key": "tokenURL", "value": "https://keycloak.apoio.celk.info/auth/realms/api/protocol/openid-connect/token", "type": "string"}]}