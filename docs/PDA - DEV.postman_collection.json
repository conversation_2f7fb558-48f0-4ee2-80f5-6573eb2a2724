{"info": {"_postman_id": "b3370e38-c3ae-44d1-a0fb-7c8798cadd38", "name": "PDA - DEV", "description": "Collection do PDA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "19523546"}, "item": [{"name": "Health", "item": [{"name": "Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/develop/health", "host": ["{{pdaServer}}"], "path": ["develop", "health"]}}, "response": []}, {"name": "<PERSON>ua<PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/develop/profile-quarkus", "host": ["{{pdaServer}}"], "path": ["develop", "profile-quarkus"]}}, "response": []}, {"name": "Version", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/develop/version", "host": ["{{pdaServer}}"], "path": ["develop", "version"]}}, "response": []}]}, {"name": "Paciente", "item": [{"name": "Medicamentos", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"method": "GET", "header": [{"key": "X-TenantID", "value": "<PERSON><PERSON><PERSON>", "type": "text", "disabled": true}], "url": {"raw": "{{pdaServer}}/csaude/paciente/medicamentos?cpf=37556380963&cns=", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "medicamentos"], "query": [{"key": "cpf", "value": "37556380963"}, {"key": "cns", "value": ""}]}}, "response": []}, {"name": "Dados <PERSON>", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"method": "GET", "header": [{"key": "X-TenantID", "value": "<PERSON><PERSON><PERSON>", "type": "text"}], "url": {"raw": "{{pdaServer}}/csaude/paciente/consultar?cpf=24293951504&cns=", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "consultar"], "query": [{"key": "cpf", "value": "24293951504"}, {"key": "cns", "value": ""}]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/paciente/prontuario?revisao=0&cpf=37556380963", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "prontuario"], "query": [{"key": "revisao", "value": "0"}, {"key": "cpf", "value": "37556380963"}]}}, "response": []}, {"name": "SalvarProntuario", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"dataHistorico\": \"\",\n    \"descricao\": \"\",\n    \"dataRegistro\": \"\",\n    \"dataConclusao\": \"\",\n    \n    \"cnesEmpresa\": \"\",\n    \n    \"nomeProfissional\": \"\",\n    \"cnsProfissional\": \"\",\n    \"cboProfissional\": \"\",\n    \"numeroRegistroProfissional\": \"\",\n    \"ufRegistroProfissional\": \"\",\n    \"codigoConselhoClasse\": \"\",\n\n    \"codigoPaciente\": 1,\n    \"nomePaciente\": \"\",\n    \"sexoPaciente\": \"\",\n    \"cpfPaciente\": \"\",\n    \"dataNascimentoPaciente\": \"\",\n    \"racaPaciente\": 1,\n\n    \"codigoSigtap\": \"\",\n\n    \"tipoAtendimento\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pdaServer}}/csaude/paciente/salvar/prontuario", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "salvar", "prontuario"]}}, "response": []}, {"name": "Exames", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/paciente/exames?cpf=37556380963&cns=", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "exames"], "query": [{"key": "cpf", "value": "37556380963"}, {"key": "cns", "value": ""}]}}, "response": []}, {"name": "Agendamentos", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/paciente/agendamentos?cpf=37556380963&cns", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "agendamentos"], "query": [{"key": "cpf", "value": "37556380963"}, {"key": "cns", "value": null}]}}, "response": []}, {"name": "Vacinas", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/paciente/vacinas?cpf=37556380963&cns=", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "paciente", "vacinas"], "query": [{"key": "cpf", "value": "37556380963"}, {"key": "cns", "value": ""}]}}, "response": []}]}, {"name": "Agenda", "item": [{"name": "Consultar Agendas Procedimento", "request": {"method": "GET", "header": [{"key": "X-TenantID", "value": "squadatenas", "type": "text"}], "url": {"raw": "{{pdaServer}}/csaude/agenda/consultarAgendasProcedimento?cpf=37556380963&cns", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "agenda", "consultarAgendasProcedimento"], "query": [{"key": "cpf", "value": "37556380963"}, {"key": "cns", "value": null}]}}, "response": []}, {"name": "Consultar Agenda Horario", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/agenda/consultarAgendaHorario?careUnitId=249972&procedureId=477238", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "agenda", "consultarAgendaHorario"], "query": [{"key": "careUnitId", "value": "249972"}, {"key": "procedureId", "value": "477238"}]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{pdaServer}}/csaude/agenda/agendarPaciente?cpf=90287001539&cns=&careUnitId=249972&timeScheduleId=123456&gridScheduleServiceId=123456&procedureId=477238", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "agenda", "agendarPaciente"], "query": [{"key": "cpf", "value": "90287001539"}, {"key": "cns", "value": ""}, {"key": "careUnitId", "value": "249972"}, {"key": "timeScheduleId", "value": "123456"}, {"key": "gridScheduleServiceId", "value": "123456"}, {"key": "procedureId", "value": "477238"}]}}, "response": []}, {"name": "Cancelar Agendamento Paciente", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{pdaServer}}/csaude/agenda/cancelarAgendamentoPaciente?idAgenda=1234567", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "agenda", "cancelarAgendamentoPaciente"], "query": [{"key": "idAgenda", "value": "1234567"}]}}, "response": []}]}, {"name": "Patients", "item": [{"name": "v1 Lista Prontuario Pacientes", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/v1/api/patients?name=<PERSON>&pageNumber=1", "host": ["{{pdaServer}}"], "path": ["v1", "api", "patients"], "query": [{"key": "name", "value": "<PERSON>"}, {"key": "pageNumber", "value": "1"}]}}, "response": []}, {"name": "v2 Lista Prontuario Pacientes Copy", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/patients?name=<PERSON>&pageNumber=1", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "patients"], "query": [{"key": "name", "value": "<PERSON>"}, {"key": "pageNumber", "value": "1"}]}}, "response": []}]}, {"name": "DynamoResource", "item": [{"name": "Municipios Clientes", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/v1/api/municipios/SC", "host": ["{{pdaServer}}"], "path": ["v1", "api", "municipios", "SC"]}}, "response": []}]}, {"name": "Care Unit", "item": [{"name": "Listar Unidades Saúde", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/careUnits/", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "careUnits", ""]}}, "response": []}, {"name": "Count <PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/careUnits/count", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "careUnits", "count"]}}, "response": []}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Lista Fila Agendamentos", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-TenantID", "value": "testeflorianopolis", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pdaServer}}/csaude/v1/filaAgendamentos?pageNumber=1&startDate=01-06-2024", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "filaAgendamentos"], "query": [{"key": "pageNumber", "value": "1"}, {"key": "startDate", "value": "01-06-2024"}]}}, "response": []}]}, {"name": "Procedure", "item": [{"name": "Listar Procedimentos Disponíveis", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/procedures/?pageNumber=1", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "procedures", ""], "query": [{"key": "pageNumber", "value": "1"}]}}, "response": []}, {"name": "Contar Procedimentos Disponíveis", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/procedures/count", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "procedures", "count"], "query": [{"key": "pageNumber", "value": "1", "disabled": true}]}}, "response": []}]}, {"name": "Professional", "item": [{"name": "Listar Profissionais", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/professionals/?pageNumber=1", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "professionals", ""], "query": [{"key": "pageNumber", "value": "1"}]}}, "response": []}, {"name": "Contar Profissionais", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/professionals/count", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "professionals", "count"]}}, "response": []}]}, {"name": "Schedule", "item": [{"name": "Listar Agendas Disponíveis", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/schedules/?pageNumber=1&amountOfDays=1", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "schedules", ""], "query": [{"key": "pageNumber", "value": "1"}, {"key": "amountOfDays", "value": "1"}]}}, "response": []}, {"name": "Count <PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/schedules/count", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "schedules", "count"]}}, "response": []}, {"name": "Criar Novas Agendas", "request": {"method": "POST", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/schedules/", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "schedules", ""]}}, "response": []}, {"name": "Cancelar Agendamentos", "request": {"method": "PUT", "header": [], "url": {"raw": "{{pdaServer}}/csaude/v1/schedules/cancel", "host": ["{{pdaServer}}"], "path": ["csa<PERSON>", "v1", "schedules", "cancel"]}}, "response": []}]}, {"name": "Token", "event": [{"listen": "test", "script": {"exec": ["var resp = pm.response.json();", "var token = resp.access_token;", "", "pm.collectionVariables.set(\"pdaToken\", token);"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "client_id", "value": "{{clientID}}", "type": "text"}, {"key": "client_secret", "value": "{{clientSecret}}", "type": "text"}]}, "url": {"raw": "{{keycloakServer}}/auth/realms/api/protocol/openid-connect/token", "host": ["{{keycloakServer}}"], "path": ["auth", "realms", "api", "protocol", "openid-connect", "token"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pdaToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "pdaServer", "value": "https://pda.apoio.celk.info", "type": "string"}, {"key": "pdaToken", "value": "", "type": "string"}, {"key": "keycloakServer", "value": "", "type": "string"}, {"key": "clientID", "value": "", "type": "string"}, {"key": "clientSecret", "value": "", "type": "string"}]}