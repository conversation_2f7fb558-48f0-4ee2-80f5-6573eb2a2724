{"id": "celk-realm", "realm": "celk-realm", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "none", "registrationAllowed": true, "registrationEmailAsUsername": false, "rememberMe": true, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": true, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "a08c4768-f374-4a79-8f8b-b160b06e53c6", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "celk-realm", "attributes": {}}, {"id": "2ce2e171-846b-46a9-994a-c3d1b8082f21", "name": "create-realm", "description": "${role_create-realm}", "composite": false, "clientRole": false, "containerId": "celk-realm", "attributes": {}}, {"id": "0d2fd44f-7e64-4424-8262-5512c3484fce", "name": "app_cidadao", "description": "Full access for App Cidadão", "composite": false, "clientRole": false, "containerId": "celk-realm", "attributes": {}}, {"id": "0b9b3080-9805-408a-bba8-6afc066cda7d", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "celk-realm", "attributes": {}}, {"id": "4c3eed7a-4d67-4f61-bc1e-0be3eed01a68", "name": "admin", "description": "${role_create-realm}", "composite": true, "composites": {"realm": ["create-realm"], "client": {"realm-management": ["view-identity-providers", "view-realm", "manage-clients", "view-events", "manage-identity-providers", "query-groups", "create-client", "view-users", "query-clients", "view-clients", "manage-users", "manage-authorization", "realm-admin", "manage-events", "impersonation", "view-authorization", "query-users", "query-realms", "manage-realm"]}}, "clientRole": false, "containerId": "celk-realm", "attributes": {}}], "client": {"agenda-cidadao-api": [{"id": "c28e6f0a-bd17-4cb7-8364-ccdf06968f89", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "d4a7ed74-9de2-453c-9aa0-be1d27682881", "attributes": {}}, {"id": "df3d3209-04a2-4cdf-b40e-ff6519a3ae4b", "name": "basic", "description": "Basic role for all celk clients.", "composite": false, "clientRole": true, "containerId": "d4a7ed74-9de2-453c-9aa0-be1d27682881", "attributes": {}}], "ck-auth-service": [{"id": "4a93a444-1355-40fb-a2b6-908237ff84a2", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "e5c286ed-43cd-483b-8b02-6de277e29a22", "attributes": {}}, {"id": "08f10055-f074-4cb3-b7f1-bbf17e4873ab", "name": "admin", "composite": false, "clientRole": true, "containerId": "e5c286ed-43cd-483b-8b02-6de277e29a22", "attributes": {}}], "realm-management": [{"id": "d554c989-4b5b-4779-9f3f-54f19f712640", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "916037e0-0b31-487a-9d08-24a0400e0765", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "c6887076-e1de-4177-84e4-f773a77adc09", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "a766acd7-9181-431e-b4cb-509d35413670", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "2cdf0520-3e03-4ff4-9d4c-d51f5df2963d", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "adf27a25-1aa0-4e0a-b771-6fac7989a278", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "4be0455f-fb1d-4ce3-af71-7f3c0ca67c20", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "7e9b7929-83a1-4b8e-beb4-bb31ff08529c", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "1d124f43-2811-4c3c-bd39-32686f72d60c", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "ee4ac1c6-3349-46dd-ba63-b52949759703", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-identity-providers", "view-realm", "manage-clients", "view-events", "manage-identity-providers", "query-groups", "create-client", "view-users", "query-clients", "view-clients", "manage-users", "manage-authorization", "manage-events", "impersonation", "view-authorization", "query-users", "query-realms", "manage-realm"]}}, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "1f3ceab4-8461-4560-9637-6aafff5f0a5c", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "4d0588fd-3e7e-496d-8a88-fa42c8716796", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "9e2d7be6-c5be-4972-9921-144f96f3c132", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "df22374e-0fd6-466d-91ef-605d93e228d9", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "45cab3e4-5e6c-4798-894e-c70e4b220f9e", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "30f6adf8-782b-40de-b7b9-12774501b6d0", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "68af89f2-ad56-4457-b047-5021f274deea", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "f973f12c-b080-4954-a2f1-8d07d176e58d", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}, {"id": "6e0a3f8a-f4a8-4354-849a-16682b8eff0b", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "attributes": {}}], "agenda-cidadao-credentials-api": [{"id": "5cad2580-aa6e-4317-ad89-064a7362b78f", "name": "basic", "composite": false, "clientRole": true, "containerId": "6b25564e-9a11-44e8-af93-0ad081b70e49", "attributes": {}}, {"id": "b8630cd3-526d-44dd-82e3-eefd0c77993e", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "6b25564e-9a11-44e8-af93-0ad081b70e49", "attributes": {}}], "security-admin-console": [], "cms": [{"id": "a81047c8-66a6-4255-8ba9-9fc594d370bb", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "db98cf4b-bd1c-49dc-b6f0-a22f1000f6ec", "attributes": {}}, {"id": "41a9d80b-7bb9-4015-ac1c-2d9ea3d61377", "name": "ombudsman", "description": "Role para <PERSON>u<PERSON>or", "composite": false, "clientRole": true, "containerId": "db98cf4b-bd1c-49dc-b6f0-a22f1000f6ec", "attributes": {}}, {"id": "60a82b8d-f406-4bc5-9ed8-6393e039d6dc", "name": "citizen", "description": "Role de cidadão", "composite": false, "clientRole": true, "containerId": "db98cf4b-bd1c-49dc-b6f0-a22f1000f6ec", "attributes": {}}], "admin-cli": [{"id": "67c025dd-f30a-453b-8f2e-4ec0793bec3f", "name": "admin-role", "composite": true, "composites": {"realm": ["admin"]}, "clientRole": true, "containerId": "9e1d4a56-7ac5-4f64-b6e5-1ba1c3a1c4fa", "attributes": {}}], "developer": [], "account-console": [], "broker": [{"id": "eb77846a-b29b-41fd-a308-d6517d928eea", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "cf45f765-d3dc-4cd2-8214-b99bfd4cf973", "attributes": {}}], "ck_saude": [{"id": "3524dd88-b9f7-414a-a7a3-07091b35e908", "name": "test_role", "composite": false, "clientRole": true, "containerId": "076e9e47-4331-42c2-8ae4-3a13b2ac6005", "attributes": {}}], "account": [{"id": "34da2900-e19f-4a77-9182-a2ebc1c048ec", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "4515a32f-5d90-4b33-95e4-8c6eae9cf0f1", "attributes": {}}, {"id": "e450427d-eb63-42a7-8a52-16a57fad156c", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "4515a32f-5d90-4b33-95e4-8c6eae9cf0f1", "attributes": {}}, {"id": "9ca123e5-2ad4-4d63-8b1d-272ecfa3a282", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "4515a32f-5d90-4b33-95e4-8c6eae9cf0f1", "attributes": {}}, {"id": "a22cf554-b75b-41dc-91e5-5249355b14ec", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "4515a32f-5d90-4b33-95e4-8c6eae9cf0f1", "attributes": {}}, {"id": "2f202dc9-40e9-4d3e-9812-ec9efbf4bbe9", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "4515a32f-5d90-4b33-95e4-8c6eae9cf0f1", "attributes": {}}, {"id": "de311797-60b7-4d77-bf2f-78d879815602", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "4515a32f-5d90-4b33-95e4-8c6eae9cf0f1", "attributes": {}}]}}, "groups": [{"id": "25f7df73-c537-4631-9e45-0946726e2cdd", "name": "cidadao", "path": "/cidadao", "attributes": {}, "realmRoles": ["app_cidadao"], "clientRoles": {"agenda-cidadao-api": ["basic"]}, "subGroups": []}], "defaultRoles": ["uma_authorization", "offline_access"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "25c7682e-cb83-4512-8f65-************", "createdTimestamp": *************, "username": "service-account-agenda-cidadao-api", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "agenda-cidadao-api", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["app_cidadao"], "clientRoles": {"agenda-cidadao-api": ["uma_protection", "basic"], "account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}, {"id": "cad05745-d516-4e1f-86b5-7c5fe63e3e78", "createdTimestamp": *************, "username": "service-account-agenda-cidadao-credentials-api", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "agenda-cidadao-credentials-api", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "app_cidadao", "uma_authorization"], "clientRoles": {"agenda-cidadao-credentials-api": ["basic", "uma_protection"], "account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}, {"id": "5156a62f-aa5b-4c30-b747-b9c51e41ceef", "createdTimestamp": *************, "username": "service-account-ck-auth-service", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "ck-auth-service", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization", "admin"], "clientRoles": {"ck-auth-service": ["uma_protection", "admin"], "account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}, {"id": "036bedf3-5457-448d-bb9d-96f1f9d78089", "createdTimestamp": *************, "username": "service-account-ck_saude", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "ck_saude", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "app_cidadao", "uma_authorization"], "clientRoles": {"account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}, {"id": "29f4ceb3-b1f3-4d5d-9340-9a690fdc392c", "createdTimestamp": *************, "username": "service-account-cms", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "cms", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"cms": ["uma_protection"], "account": ["manage-account", "view-profile"]}, "notBefore": **********, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "4515a32f-5d90-4b33-95e4-8c6eae9cf0f1", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/celk-realm/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "defaultRoles": ["manage-account", "view-profile"], "redirectUris": ["/realms/celk-realm/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "1b508a10-8b1d-4f37-a87d-5c30f9454341", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/celkrealm/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/realms/celkrealm/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "d73afb0d-6090-4100-99cb-e819ae39dce5", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9e1d4a56-7ac5-4f64-b6e5-1ba1c3a1c4fa", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "d4a7ed74-9de2-453c-9aa0-be1d27682881", "clientId": "agenda-cidadao-api", "rootUrl": "${authBaseUrl}", "adminUrl": "${authBaseUrl}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["${authBaseUrl}/*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": true, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "login_theme": "keycloak", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "d31f0601-9c4a-4816-a5a0-4473322069c4", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "394798ae-b02a-4a42-b3be-7b6fbd6c094c", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "usermodel.clientRoleMapping.clientId": "celkclient"}}, {"id": "42df7947-a612-4ed0-a622-81e9ef4ffb85", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "15ad9e78-a8a8-4737-8f99-b80d5f4c1656", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}], "defaultClientScopes": ["address", "role_list", "offline_access", "microprofile-jwt", "email"], "optionalClientScopes": ["web-origins", "phone", "profile", "roles"], "authorizationSettings": {"allowRemoteResourceManagement": true, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "Default Resource", "type": "urn:celkclient:resources:default", "ownerManagedAccess": false, "attributes": {}, "_id": "0ce4571d-41f1-45c6-a336-c6fe5d3a3e7f", "uris": ["/*"]}], "policies": [{"id": "be3409f7-09cc-48e4-a35c-c798d11aa109", "name": "Default Policy", "description": "A policy that grants access only for users within this realm", "type": "js", "logic": "POSITIVE", "decisionStrategy": "AFFIRMATIVE", "config": {"code": "// by default, grants any permission associated with this policy\n$evaluation.grant();\n"}}, {"id": "1008d2a6-2134-457c-be74-55f2e085a808", "name": "Default Permission", "description": "A permission that applies to the default resource type", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:celkclient:resources:default", "applyPolicies": "[\"Default Policy\"]"}}], "scopes": [], "decisionStrategy": "UNANIMOUS"}}, {"id": "6b25564e-9a11-44e8-af93-0ad081b70e49", "clientId": "agenda-cidadao-credentials-api", "rootUrl": "${authBaseUrl}", "adminUrl": "${authBaseUrl}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "f07cf83b-db56-44cd-8fcc-305b45678cae", "redirectUris": ["${authBaseUrl}/*"], "webOrigins": ["${authBaseUrl}"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": true, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "bc4c3f8b-b34b-4349-817c-a38f8ea82937", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"id": "61b14a0e-05c6-45c6-bfc7-20d92cee400a", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "146c9ce7-ab12-425c-8ba8-bb6d794e30b4", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "cf45f765-d3dc-4cd2-8214-b99bfd4cf973", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "e5c286ed-43cd-483b-8b02-6de277e29a22", "clientId": "ck-auth-service", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": true, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "c7df9b68-f2ac-4097-8588-f4192a14bb44", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "64d1b3ab-e4cd-460a-b817-1f33630813ca", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "cafcd9e8-a003-471f-9c36-8c3975387c69", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "authorizationSettings": {"allowRemoteResourceManagement": true, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "Default Resource", "type": "urn:ck-auth-service:resources:default", "ownerManagedAccess": false, "attributes": {}, "_id": "1146f424-1f7a-40a2-b039-1b92607270cb", "uris": ["/*"]}], "policies": [{"id": "5155e4d7-0b63-456e-b5d0-720a3500f247", "name": "Default Policy", "description": "A policy that grants access only for users within this realm", "type": "js", "logic": "POSITIVE", "decisionStrategy": "AFFIRMATIVE", "config": {"code": "// by default, grants any permission associated with this policy\n$evaluation.grant();\n"}}, {"id": "b628fdca-6e90-47d5-ab57-2ab8aad7842a", "name": "Default Permission", "description": "A permission that applies to the default resource type", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:ck-auth-service:resources:default", "applyPolicies": "[\"Default Policy\"]"}}], "scopes": [], "decisionStrategy": "UNANIMOUS"}}, {"id": "076e9e47-4331-42c2-8ae4-3a13b2ac6005", "clientId": "ck_saude", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "477c422b-4f0e-45d1-944f-1e230a49f31e", "redirectUris": ["/saude/api"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "ecd58924-907b-4d7c-9db5-fe3ad412b7e8", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "76a335e8-2895-44ce-b885-ea6329639bec", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"id": "7fe6c736-b74d-44e8-af41-a40e4cc0f73a", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "db98cf4b-bd1c-49dc-b6f0-a22f1000f6ec", "clientId": "cms", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": true, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "f307aed1-e940-4d4d-972f-8d0c426b7dbe", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"id": "ecf510d2-8e4f-408c-a015-c1f6a0d4cb5d", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "d40cb613-9eec-4a1a-b051-30ac9b2e9090", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "authorizationSettings": {"allowRemoteResourceManagement": true, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "Default Resource", "type": "urn:cms:resources:default", "ownerManagedAccess": false, "attributes": {}, "_id": "d9b4a7b9-a88a-4319-b839-8deb39bdbe93", "uris": ["/*"]}], "policies": [{"id": "3c10bc1a-baff-4483-8b8a-da27f3289bd4", "name": "Default Policy", "description": "A policy that grants access only for users within this realm", "type": "js", "logic": "POSITIVE", "decisionStrategy": "AFFIRMATIVE", "config": {"code": "// by default, grants any permission associated with this policy\n$evaluation.grant();\n"}}, {"id": "6bd6eec8-d626-48a9-ae59-d10a56d593ca", "name": "Default Permission", "description": "A permission that applies to the default resource type", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:cms:resources:default", "applyPolicies": "[\"Default Policy\"]"}}], "scopes": [], "decisionStrategy": "UNANIMOUS"}}, {"id": "32d58945-d7b5-4a30-b854-0067bcdf4e60", "clientId": "developer", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "1faa8b1b-ce9b-437a-8cf2-944cf3289da9", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "39a1018b-0fb7-4c71-8647-ed513d82c799", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/celk-realm/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/admin/celk-realm/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "17e09adb-3f47-44b4-89b1-d170fd1e7ec7", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "f78367fb-84b6-4e16-80b8-3a41caef88b5", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "d43b6d96-ead4-423c-8b26-5c76cd6cd443", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "3efe7f09-7e2c-4481-bd16-9dbf95511c49", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "cabcca73-ce67-4854-a740-7668d3548f66", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "ea6dba89-fe09-4f0e-9aa7-3efc0b67e203", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "73250097-50fa-4734-b7fe-afda75f9b426", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "ca453224-2ce9-49ec-9e93-ba56b5b4845d", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "dfa17713-58ff-489c-9cfe-d0cb533e14e2", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "d122528d-5976-485d-9bbe-774e24da4808", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "07ed7c49-a0c3-4434-8d01-eb6f20a082bf", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "6d9d9e3e-1b25-49ee-832c-25a275348017", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "0d53bcff-bac9-4f53-b0de-fd749eb7d36f", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "7d3856aa-28d8-4d64-a343-35cdcfe43796", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "552a047a-fee1-4421-a33c-3d9b01cdb1a6", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "425f676c-5eba-4b29-ac06-919b7264d0a6", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "ff3e7e52-0ede-4b6b-964e-58a0c163ea7d", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "79387d62-50dc-4b97-9556-0eb66c505378", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "60771706-3cf6-4e15-9d25-213be0ed22a5", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "cb3ca360-19cf-40d2-ab16-1378cb7c120a", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "fe2008e9-f2f2-41ab-a223-51ced52799c5", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "1a814742-9ad6-4956-862e-bc23a6f92841", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "b9ec58f7-641e-4fbf-9f6f-1ab15bcc5988", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "fa950e66-b2e9-4b34-b982-58a1f5dbdd54", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "e53e4f91-969c-4b53-8b52-b401416b936a", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "07d73a92-6f63-4815-b1d1-1effadf9fd23", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "476cf8aa-e750-42be-9bd1-b52ddd7853ac", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "69d01567-f300-4168-a13d-6d38e9329c84", "name": "tenant", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "tenant", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "tenant", "jsonType.label": "String"}}, {"id": "a00c44f8-c45c-4797-98e3-3f640511e9f0", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}]}, {"id": "4d22084a-2219-41b9-831d-5f8b2a0af805", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "622fd21c-e467-4766-a117-8fb59791b328", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "f3c1a808-2b2a-4a53-b3a6-f4b153aee136", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "2b9f28ec-1322-4aab-ae87-39186d1dfa68", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "c36520dc-96b7-4f2c-afbb-3d502333ad37", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "29b112dc-f393-4e84-ad85-d2bc0423c281", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}]}, {"id": "e7bf8e2d-f9f1-477d-827f-784272ff07ad", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "7df15b7e-757f-4c00-9370-d5a5fb64c647", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "smtpServer": {"password": "**********", "starttls": "", "auth": "true", "port": "465", "host": "**********************************", "from": "<EMAIL>", "fromDisplayName": "<PERSON><PERSON>", "ssl": "true", "user": "AKIA4UJSUETRSXUYNLUZ"}, "loginTheme": "keycloak", "accountTheme": "keycloak", "adminTheme": "keycloak", "emailTheme": "keycloak", "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "bd4e58fb-e0c4-4d3b-88ad-90e0290aea69", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "d040453c-0879-4aba-9cae-19f2669f7c64", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "12e84786-bdeb-4be3-a093-f7aae1a2d0d9", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "7badefba-e3c8-4ba0-ae4a-ab7811790537", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper"]}}, {"id": "e56149c5-d432-47cc-9116-505fbad84879", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-sha256-pairwise-sub-mapper", "oidc-address-mapper", "oidc-full-name-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-usermodel-property-mapper", "oidc-usermodel-attribute-mapper"]}}, {"id": "8258705c-46d3-4a3b-987f-98f78a01d3ab", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "6658f993-1250-4418-aed0-b86bf2158f71", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "c57b25bf-ba81-465b-9a56-fc4be3532722", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}], "org.keycloak.keys.KeyProvider": [{"id": "aa7641aa-f92b-448f-bd5e-6c252c6f2b16", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "7c6e2682-8870-494b-b5e4-263afb8ae49c", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "60535e84-df5f-4b23-916f-7f25115385d6", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS256"]}}]}, "internationalizationEnabled": true, "supportedLocales": ["de", "no", "ru", "sv", "pt-BR", "lt", "en", "it", "fr", "zh-CN", "es", "ja", "sk", "pl", "ca", "nl", "tr"], "defaultLocale": "pt-BR", "authenticationFlows": [{"id": "9d924d3b-4d69-4067-8ec5-1d429621e3f3", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "84598ca5-2002-41fd-b7b5-6f2f977f1c4a", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "26d67f8c-4ce9-4207-8c8a-61d3563f2d44", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "c80fbbbd-f95c-4aad-be5a-e0e648e1bd39", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "451d172c-09ff-45af-b50d-06b38b8efee8", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "7372d471-15a1-477a-a8ee-f8601d08ef9f", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Account verification options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "cac71c17-a38d-43d8-b111-22181fa3dd31", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "295f94bc-1223-4d54-bc61-82b8f3d8edb0", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "977dd642-ab12-41d6-8acc-36ef27ddc48b", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "73d98ac2-8956-4460-84f7-c94cc9ca6353", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "cc3630e6-fc61-41ce-8d3e-139be5c07b37", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "51185291-d142-4eb2-a731-d034f73b095d", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 30, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "c5e49282-d62c-439d-8365-dc71a1bcc921", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "2de7b93e-b258-4a7e-8c9d-a97b817f30ff", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "User creation or linking", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "5fc09841-b44d-4895-aa9d-a892aaf20813", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "c6ba2238-5fb7-4e89-91e8-654c3aa1abb6", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Authentication Options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "b387e4e4-b5af-4235-9549-09b496d6fa22", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "0b19f146-a4c9-4269-b3e5-4c6b54243d86", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "1d7b0ab8-9442-4240-87d9-9ef57bfd8648", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 40, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "3c9b0bf2-30e1-4feb-8414-b2fe8b6cac04", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "b0b3f8cf-dbf4-4bed-8e72-580f02d3c818", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "f3c4c27d-ae90-4009-a4d8-14f1d3756c83", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"clientSessionIdleTimeout": "0", "clientSessionMaxLifespan": "0"}, "keycloakVersion": "10.0.1", "userManagedAccessAllowed": false}