{"clk_bases": [{"PutRequest": {"Item": {"tenantName": {"S": "localhost"}, "uf": {"S": "GO"}, "dbname": {"S": "teste_goiania"}, "user": {"S": "celk"}, "pass": {"S": "TcelkDB"}, "port": {"S": "5432"}, "urlDatabase": {"S": "dbteste11.celk.com.br"}, "urlDatabaseReading": {"S": "dbteste11.celk.com.br"}, "urlDomain": {"S": "http://algumendpointdosma.celk.com.br"}}}}, {"PutRequest": {"Item": {"tenantName": {"S": "localanapolis"}, "uf": {"S": "GO"}, "dbname": {"S": "local_anapolis"}, "user": {"S": "celk"}, "pass": {"S": "celk"}, "port": {"S": "5432"}, "urlDatabase": {"S": "localhost"}, "urlDatabaseReading": {"S": "localhost"}, "urlDomain": {"S": "http://algumendpointdosma:8080"}}}}]}