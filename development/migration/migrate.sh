sleep 5s

echo "Deleting pre-existing table \n"
aws dynamodb delete-table \
	--table-name clk_bases \
	--endpoint-url http://dynamodb:8000 \
	--no-cli-pager

echo "Creating table clk_bases \n"
aws dynamodb create-table \
	--cli-input-json "$(cat migration/create_table.json)" \
	--endpoint-url http://dynamodb:8000 \
	--no-cli-pager

echo "Loading data into fclk_bases table \n"
AWS_ACCESS_KEY_ID=$(aws configure get aws_access_key_id) \
AWS_SECRET_ACCESS_KEY=$(aws configure get aws_secret_access_key) \
aws dynamodb batch-write-item \
	--request-items "$(cat migration/load_data.json)" \
	--endpoint-url http://dynamodb:8000 \
	--no-cli-pager
