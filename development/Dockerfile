# syntax = docker/dockerfile:experimental
## Stage 1 : build with maven builder image with native capabilities
FROM tperdriau/ffmpeg-centos-quarkus-maven:20.0.0-java11 AS build
COPY ../pom.xml pom.xml
COPY ../src src/
RUN mkdir -p .m2
COPY .m2/settings.xml .m2/settings.xml

RUN --mount=type=cache,target=/home/<USER>/.m2/repository,mode=0777,uid=1001,gid=1001 \
    mvn -s .m2/settings.xml clean package -Pnative -U -Dmaven.test.skip=true
#RUN mvn package -Dmaven.test.skip=true

## Stage 2 : intermediate container to copy the needed dynamic libraries
FROM debian:10-slim AS debian

## Stage 3 : create the final docker image
FROM scratch
COPY --from=build /project/target/*-runner /bin/app
COPY --from=debian /lib64/ld-linux-x86-64.so.2 /lib64/ld-linux-x86-64.so.2
COPY --from=debian /lib/x86_64-linux-gnu/ld-2.28.so \
    /lib/x86_64-linux-gnu/libm.so.6 /lib/x86_64-linux-gnu/libm-2.28.so \
    /lib/x86_64-linux-gnu/libpthread.so.0 /lib/x86_64-linux-gnu/libpthread-2.28.so \
    /lib/x86_64-linux-gnu/libdl.so.2 /lib/x86_64-linux-gnu/libdl-2.28.so \
    /lib/x86_64-linux-gnu/libz.so.1 /lib/x86_64-linux-gnu/libz.so.1.2.11 \
    /lib/x86_64-linux-gnu/librt.so.1 /lib/x86_64-linux-gnu/librt-2.28.so \
    /lib/x86_64-linux-gnu/libc.so.6 /lib/x86_64-linux-gnu/libc-2.28.so \
    /lib/x86_64-linux-gnu/libgcc_s.so.1 \
    /usr/lib/x86_64-linux-gnu/libstdc++.so.6 \
    /lib/x86_64-linux-gnu/
EXPOSE 8080
CMD ["/bin/app", "-Dquarkus.http.host=0.0.0.0"]
