FROM tperdriau/ffmpeg-centos-quarkus-maven:20.0.0-java11 as maven
COPY pom.xml pom.xml
COPY src/ src/
COPY .m2/ .m2/
RUN mvn -s .m2/settings.xml clean package -Dmaven.test.skip=true -Dmaven.repo.local=.m2/repository


FROM adoptopenjdk/openjdk11-openj9
RUN mkdir /opt/app
WORKDIR /opt/app
COPY --from=maven  /project/target/*-runner.jar japp.jar
COPY --from=maven /project/target/lib/ lib
ENTRYPOINT java -jar \
-XX:+UseSerialGC -Xss512k -Xms32m -Xmx64m -XX:MaxMetaspaceSize=72m -XX:MaxRAM=72m \
japp.jar
