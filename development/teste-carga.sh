#!/bin/bash

#Este script foi desenvolvido para testar a capacidade do PDA de gerenciar as conexões no pool com o banco de dados
#Ele, cria as conexões por tenant e por endpoint, ou seja se tiver 6 tenants na lista e 4 endpoints, serão 24 conexões

#Para verificar as conexões criadas no banco de dados execute o SQL abaixo
# SELECT pid, state, xact_start, query_start, query, *
# FROM pg_stat_activity
# where state in ('active', 'idle in transaction', 'idle')
# and application_name='PDA'
# order by 4

baseUrlPda="http://localhost:8081";
statusCode=$(curl -sS -I "${baseUrlPda}/health" | head -n 1 | cut -d$' ' -f2);
tenantsDisponiveis=("testecriciuma" "testecariacica" "testearaguaina" "testeportobelo" "testeviamao" "testeicara");

clientId="developer-api";
clientSecret="b3bf9a20-fa8a-4dd1-9b2c-57cfb2dc59a0";
authEndpoint="https://keycloak.apoio.celk.info/auth/realms/api/protocol/openid-connect/token";
jsonToken="";
bearerToken="";

endpointConsultarAgendas="${baseUrlPda}/csaude/agenda/consultarAgendasProcedimento";
endpointListaAgendamentos="${baseUrlPda}/csaude/v1/filaAgendamentos?pageNumber=1&startDate=05-05-2025";
endpointAtendimentosRealizados="${baseUrlPda}/csaude/v1/atendimentosRealizados?pageNumber=1&startDate=2025-05";
endpointUnidadesSaude="${baseUrlPda}/csaude/v1/careUnits?pageNumber=1";


function consultar_endpoints() {
  local tenant=$1;
  shift;
  local hXtenantId="X-TenantID: ${tenant}";

  echo "--------INICIO--------";
  echo "TENANT: ${tenant}";

  for endpoint in "$@"; do
    echo "endpoint: ${endpoint}"
    resposta=$(curl -sS -X GET -H "${hAuthorization}" -H "${hXtenantId}" "${endpoint}");
    #echo "$resposta"
  done

  echo "TENANT: ${tenant}";
  echo "--------FIM--------";
}


if [ "${statusCode}" = '200' ]; then

  body="grant_type=client_credentials&client_id=${clientId}&client_secret=${clientSecret}";
  jsonToken=$(curl -sS -X POST -H "Content-Type: application/x-www-form-urlencoded" -d $body $authEndpoint);
  bearerToken=$( echo "${jsonToken}" | jq -r '.access_token');
  hAuthorization="Authorization: Bearer ${bearerToken}";

  for tenant in "${tenantsDisponiveis[@]}"; do
    (
      consultar_endpoints "${tenant}" \
      "${endpointConsultarAgendas}" \
      "${endpointListaAgendamentos}" \
      "${endpointAtendimentosRealizados}" \
      "${endpointUnidadesSaude}";
    ) &

  done;
  wait
fi;
