# Cache global para as dependências do Maven, acelerando os jobs
cache:
  key: $CI_PROJECT_NAME
  paths:
    - .m2

# Variáveis globais para os comandos do Maven
variables:
  MAVEN_CLI_OPTS: '-s .m2/settings.xml --batch-mode -Dsonar.host.url=$SONAR_URL -Dmaven.repo.local=.m2/repository -Dsonar.login=$SONAR_TOKEN'
  MAVEN_OPTS: '-Dmaven.repo.local=.m2/repository'

stages:
  - test
  - build

# Template base para configuração de jobs.
# Define a imagem e os scripts de preparação que são comuns para os jobs de teste e build.
.job_setup:
  # Usamos uma imagem que já contém o cliente Docker.
  image: docker:19.03.12
  before_script:
    # Instala todas as dependências necessárias de uma só vez.
    - apk add --no-cache py3-pip jq openjdk11-jre git maven
    - pip3 install yq
    # Configura a chave SSH para operações de git que possam ser necessárias.
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh && chmod 700 ~/.ssh
    - echo $(ssh-keyscan gitlab.com) >> ~/.ssh/known_hosts && chmod 644 ~/.ssh/known_hosts
    - git config --global user.email "$GIT_USER_EMAIL"
    - git config --global user.name "$GIT_USER_NAME"
    # Prepara o arquivo de configurações do Maven.
    - mkdir -p .m2
    - cp $M2_SETTINGS .m2/settings.xml
    # Garante que a variável JAVA_HOME esteja definida.
    - export JAVA_HOME=/usr/lib/jvm/default-jvm/jre

# Job para rodar os testes da aplicação.
build and test:
  stage: test
  extends: .job_setup
  script:
    # Este job agora pode rodar testes de integração que usam testcontainers,
    # pois eles se comunicarão com o Docker do host através do socket.
    - mvn $MAVEN_CLI_OPTS help:evaluate -Dexpression=settings.localRepository
    # - mvn $MAVEN_CLI_OPTS clean verify sonar:sonar -Dsonar.qualitygate.wait=true
  only:
    - master
    - dev
    - develop
    - development
    - merge_requests
    - /^release\/.*$/
  artifacts:
    reports:
      junit:
        - target/surefire-reports/TEST-*.xml
        - target/failsafe-reports/TEST-*.xml

# Job para construir e enviar a imagem Docker para o registry.
docker_build_and_push:
  stage: build
  extends: .job_setup
  script:
    # 1. Checagem de Versão
    - export POM_VERSION=$(cat pom.xml | xq -r  '.project.version')
    - export TAG_VERSION=${CI_COMMIT_TAG#"v"}
    - if [ "$POM_VERSION" != "$TAG_VERSION" ]; then echo "ERROR!! version in pom.xml ($POM_VERSION) does not match git tag version ($TAG_VERSION)!"; exit 1; fi

    # 2. Login no Docker Registry
    - mkdir -p ~/.docker
    - echo $DOCKER_REGISTRY_CREDENTIALS > ~/.docker/config.json

    # 3. Construção da Imagem
    # O Jib/Quarkus detectará automaticamente o socket /var/run/docker.sock do host,
    # pois a variável DOCKER_HOST não está mais definida.
    - ./mvnw package -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -Dquarkus.container-image.build=true -Dquarkus.container-image.registry=$DOCKER_REGISTRY -Dquarkus.container-image.name=pda -Dquarkus.container-image.tag=$POM_VERSION -Dquarkus.container-image.group=celksistemas

    # 4. Push da Imagem
    # O comando 'docker' usará o socket do host para fazer o push.
    - docker push $DOCKER_REGISTRY/celksistemas/pda:$POM_VERSION
  rules:
    # Este job roda apenas quando uma tag de git é criada.
    - if: '$CI_COMMIT_TAG =~ /^v.*$/'
 